<?php
session_start();

// تحديد اللغة
$lang = isset($_SESSION['lang']) ? $_SESSION['lang'] : 'ar';

// نصوص بسيطة للصفحة
$text = [
    'ar' => [
        'page_not_found' => 'الصفحة غير موجودة',
        'error_404' => 'خطأ 404',
        'sorry_message' => 'عذراً، الصفحة التي تبحث عنها غير موجودة',
        'possible_reasons' => 'الأسباب المحتملة:',
        'reason_1' => 'الرابط الذي استخدمته قديم أو غير صحيح',
        'reason_2' => 'تم حذف الصفحة أو نقلها إلى مكان آخر',
        'reason_3' => 'حدث خطأ في كتابة الرابط',
        'what_to_do' => 'ماذا يمكنك فعله:',
        'action_1' => 'تحقق من صحة الرابط',
        'action_2' => 'استخدم شريط البحث للعثور على ما تبحث عنه',
        'action_3' => 'عد إلى الصفحة الرئيسية',
        'go_home' => 'العودة للصفحة الرئيسية',
        'contact_us' => 'اتصل بنا',
        'search_placeholder' => 'ابحث عن...'
    ],
    'en' => [
        'page_not_found' => 'Page Not Found',
        'error_404' => 'Error 404',
        'sorry_message' => 'Sorry, the page you are looking for does not exist',
        'possible_reasons' => 'Possible reasons:',
        'reason_1' => 'The link you used is old or incorrect',
        'reason_2' => 'The page has been deleted or moved',
        'reason_3' => 'There was an error in typing the link',
        'what_to_do' => 'What you can do:',
        'action_1' => 'Check the link for accuracy',
        'action_2' => 'Use the search bar to find what you are looking for',
        'action_3' => 'Go back to the homepage',
        'go_home' => 'Go to Homepage',
        'contact_us' => 'Contact Us',
        'search_placeholder' => 'Search for...'
    ]
];

$t = $text[$lang];

// تسجيل محاولة الوصول للصفحة غير الموجودة
error_log("404 Error: " . $_SERVER['REQUEST_URI'] . " - IP: " . $_SERVER['REMOTE_ADDR'] . " - User Agent: " . $_SERVER['HTTP_USER_AGENT']);

// تعيين رمز الاستجابة 404
http_response_code(404);
?>
<!DOCTYPE html>
<html lang="<?php echo $lang; ?>" dir="<?php echo $lang == 'ar' ? 'rtl' : 'ltr'; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $t['error_404']; ?> - حكيم</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .error-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 3rem;
            text-align: center;
            max-width: 600px;
            margin: 2rem;
        }
        
        .error-number {
            font-size: 8rem;
            font-weight: 700;
            color: #667eea;
            line-height: 1;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .error-icon {
            font-size: 4rem;
            color: #667eea;
            margin-bottom: 2rem;
            animation: bounce 2s infinite;
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 50px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        
        .btn-outline-primary {
            border: 2px solid #667eea;
            color: #667eea;
            border-radius: 50px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-outline-primary:hover {
            background: #667eea;
            border-color: #667eea;
            transform: translateY(-2px);
        }
        
        .search-box {
            border-radius: 50px;
            border: 2px solid #e9ecef;
            padding: 12px 20px;
            transition: all 0.3s ease;
        }
        
        .search-box:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .reasons-list {
            text-align: <?php echo $lang == 'ar' ? 'right' : 'left'; ?>;
            background: #f8f9fa;
            border-radius: 15px;
            padding: 1.5rem;
            margin: 2rem 0;
        }
        
        .reasons-list ul {
            margin: 0;
            padding-<?php echo $lang == 'ar' ? 'right' : 'left'; ?>: 1.5rem;
        }
        
        .reasons-list li {
            margin-bottom: 0.5rem;
            color: #6c757d;
        }
        
        .floating-shapes {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }
        
        .shape {
            position: absolute;
            opacity: 0.1;
            animation: float 6s ease-in-out infinite;
        }
        
        .shape:nth-child(1) {
            top: 10%;
            left: 10%;
            animation-delay: 0s;
        }
        
        .shape:nth-child(2) {
            top: 20%;
            right: 10%;
            animation-delay: 2s;
        }
        
        .shape:nth-child(3) {
            bottom: 10%;
            left: 20%;
            animation-delay: 4s;
        }
        
        @keyframes float {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-20px);
            }
        }
    </style>
</head>
<body>
    <!-- الأشكال المتحركة في الخلفية -->
    <div class="floating-shapes">
        <div class="shape">
            <i class="fas fa-user-md fa-3x"></i>
        </div>
        <div class="shape">
            <i class="fas fa-hospital fa-3x"></i>
        </div>
        <div class="shape">
            <i class="fas fa-stethoscope fa-3x"></i>
        </div>
    </div>
    
    <div class="error-container">
        <div class="error-icon">
            <i class="fas fa-search"></i>
        </div>
        
        <div class="error-number">404</div>
        
        <h1 class="h3 mb-3 text-dark"><?php echo $t['page_not_found']; ?></h1>
        
        <p class="text-muted mb-4"><?php echo $t['sorry_message']; ?></p>
        
        <!-- أسباب محتملة -->
        <div class="reasons-list">
            <h5 class="text-primary mb-3"><?php echo $t['possible_reasons']; ?></h5>
            <ul class="text-muted">
                <li><?php echo $t['reason_1']; ?></li>
                <li><?php echo $t['reason_2']; ?></li>
                <li><?php echo $t['reason_3']; ?></li>
            </ul>
        </div>
        
        <!-- ما يمكن فعله -->
        <div class="reasons-list">
            <h5 class="text-success mb-3"><?php echo $t['what_to_do']; ?></h5>
            <ul class="text-muted">
                <li><?php echo $t['action_1']; ?></li>
                <li><?php echo $t['action_2']; ?></li>
                <li><?php echo $t['action_3']; ?></li>
            </ul>
        </div>
        
        <!-- مربع البحث -->
        <div class="mb-4">
            <form action="search.php" method="GET" class="d-flex gap-2">
                <input type="text" name="q" class="form-control search-box flex-grow-1" 
                       placeholder="<?php echo $t['search_placeholder']; ?>" required>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search"></i>
                </button>
            </form>
        </div>
        
        <!-- أزرار التنقل -->
        <div class="d-flex gap-3 justify-content-center flex-wrap">
            <a href="index.php" class="btn btn-primary">
                <i class="fas fa-home me-2"></i>
                <?php echo $t['go_home']; ?>
            </a>
            
            <a href="public/login.php" class="btn btn-outline-primary">
                <i class="fas fa-sign-in-alt me-2"></i>
                تسجيل الدخول
            </a>
            
            <a href="mailto:<EMAIL>" class="btn btn-outline-primary">
                <i class="fas fa-envelope me-2"></i>
                <?php echo $t['contact_us']; ?>
            </a>
        </div>
        
        <!-- معلومات إضافية -->
        <div class="mt-4 pt-4 border-top">
            <small class="text-muted">
                إذا كنت تعتقد أن هذا خطأ، يرجى 
                <a href="mailto:<EMAIL>" class="text-decoration-none">الاتصال بالدعم الفني</a>
            </small>
        </div>
        
        <!-- تغيير اللغة -->
        <div class="mt-3">
            <div class="btn-group btn-group-sm">
                <a href="?lang=ar" class="btn btn-sm <?php echo $lang == 'ar' ? 'btn-primary' : 'btn-outline-primary'; ?>">
                    العربية
                </a>
                <a href="?lang=en" class="btn btn-sm <?php echo $lang == 'en' ? 'btn-primary' : 'btn-outline-primary'; ?>">
                    English
                </a>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // تأثير بصري إضافي
        document.addEventListener('DOMContentLoaded', function() {
            // إضافة تأثير hover للأزرار
            const buttons = document.querySelectorAll('.btn');
            buttons.forEach(button => {
                button.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                });
                
                button.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });
            
            // التركيز على مربع البحث
            const searchInput = document.querySelector('input[name="q"]');
            if (searchInput) {
                searchInput.focus();
            }
        });
        
        // إرسال إحصائية الخطأ (اختياري)
        if (typeof gtag !== 'undefined') {
            gtag('event', 'page_view', {
                page_title: '404 Error',
                page_location: window.location.href
            });
        }
    </script>
</body>
</html>
