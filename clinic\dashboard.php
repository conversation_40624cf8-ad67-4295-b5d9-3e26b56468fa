<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول
require_login();

// التحقق من صلاحية العيادة
if (!isset($_SESSION['clinic_id'])) {
    header('Location: ../public/login.php');
    exit();
}

require_clinic_access($pdo, $_SESSION['clinic_id']);

// تحديد اللغة
$lang = isset($_SESSION['lang']) ? $_SESSION['lang'] : 'ar';
require_once "../lang/{$lang}.php";

$clinic_id = $_SESSION['clinic_id'];

// جلب بيانات العيادة
try {
    $stmt = $pdo->prepare("SELECT * FROM clinics WHERE id = ?");
    $stmt->execute([$clinic_id]);
    $clinic = $stmt->fetch();
    
    if (!$clinic) {
        header('Location: ../public/login.php');
        exit();
    }
    
    // التحقق من حالة الاشتراك
    $subscription_status = check_subscription_status($pdo, $clinic_id);
    
    // جلب الإحصائيات
    // إجمالي المرضى
    $stmt = $pdo->prepare("SELECT COUNT(*) as total_patients FROM patients WHERE clinic_id = ?");
    $stmt->execute([$clinic_id]);
    $total_patients = $stmt->fetch()['total_patients'];
    
    // مواعيد اليوم
    $stmt = $pdo->prepare("SELECT COUNT(*) as today_appointments FROM appointments WHERE clinic_id = ? AND appointment_date = CURDATE()");
    $stmt->execute([$clinic_id]);
    $today_appointments = $stmt->fetch()['today_appointments'];
    
    // الزيارات هذا الشهر
    $stmt = $pdo->prepare("SELECT COUNT(*) as monthly_visits FROM visits WHERE clinic_id = ? AND MONTH(visit_date) = MONTH(CURDATE()) AND YEAR(visit_date) = YEAR(CURDATE())");
    $stmt->execute([$clinic_id]);
    $monthly_visits = $stmt->fetch()['monthly_visits'];
    
    // الإيرادات الشهرية
    $stmt = $pdo->prepare("SELECT SUM(total_amount) as monthly_revenue FROM invoices WHERE clinic_id = ? AND payment_status = 'paid' AND MONTH(invoice_date) = MONTH(CURDATE()) AND YEAR(invoice_date) = YEAR(CURDATE())");
    $stmt->execute([$clinic_id]);
    $monthly_revenue = $stmt->fetch()['monthly_revenue'] ?? 0;
    
    // المدفوعات المعلقة
    $stmt = $pdo->prepare("SELECT SUM(total_amount - paid_amount) as pending_payments FROM invoices WHERE clinic_id = ? AND payment_status IN ('pending', 'partial')");
    $stmt->execute([$clinic_id]);
    $pending_payments = $stmt->fetch()['pending_payments'] ?? 0;
    
    // المرضى الجدد هذا الشهر
    $stmt = $pdo->prepare("SELECT COUNT(*) as new_patients FROM patients WHERE clinic_id = ? AND MONTH(created_at) = MONTH(CURDATE()) AND YEAR(created_at) = YEAR(CURDATE())");
    $stmt->execute([$clinic_id]);
    $new_patients = $stmt->fetch()['new_patients'];
    
    // مواعيد الأسبوع القادم
    $stmt = $pdo->prepare("SELECT COUNT(*) as next_week_appointments FROM appointments WHERE clinic_id = ? AND appointment_date BETWEEN DATE_ADD(CURDATE(), INTERVAL 1 DAY) AND DATE_ADD(CURDATE(), INTERVAL 7 DAY)");
    $stmt->execute([$clinic_id]);
    $next_week_appointments = $stmt->fetch()['next_week_appointments'];
    
    // الوصفات هذا الشهر
    $stmt = $pdo->prepare("SELECT COUNT(*) as monthly_prescriptions FROM prescriptions WHERE clinic_id = ? AND MONTH(created_at) = MONTH(CURDATE()) AND YEAR(created_at) = YEAR(CURDATE())");
    $stmt->execute([$clinic_id]);
    $monthly_prescriptions = $stmt->fetch()['monthly_prescriptions'];
    
    // مواعيد اليوم التفصيلية
    $stmt = $pdo->prepare("
        SELECT a.*, p.full_name as patient_name, p.phone as patient_phone, 
               cu.full_name as doctor_name
        FROM appointments a 
        JOIN patients p ON a.patient_id = p.id 
        JOIN clinic_users cu ON a.doctor_id = cu.id 
        WHERE a.clinic_id = ? AND a.appointment_date = CURDATE() 
        ORDER BY a.appointment_time ASC
    ");
    $stmt->execute([$clinic_id]);
    $today_appointments_details = $stmt->fetchAll();
    
    // أحدث المرضى
    $stmt = $pdo->prepare("SELECT * FROM patients WHERE clinic_id = ? ORDER BY created_at DESC LIMIT 5");
    $stmt->execute([$clinic_id]);
    $recent_patients = $stmt->fetchAll();
    
} catch (Exception $e) {
    $error_message = 'حدث خطأ في جلب البيانات';
}
?>
<!DOCTYPE html>
<html lang="<?php echo $lang; ?>" dir="<?php echo $lang == 'ar' ? 'rtl' : 'ltr'; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم العيادة - <?php echo htmlspecialchars($clinic['clinic_name']); ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../assets/css/style.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body class="bg-light">
    <!-- شريط التنبيه للاشتراك -->
    <?php if ($subscription_status['status'] == 'expiring'): ?>
        <div class="alert alert-warning alert-dismissible fade show mb-0" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>تنبيه:</strong> <?php echo $subscription_status['message']; ?>
            <a href="subscription.php" class="btn btn-sm btn-outline-warning ms-2">تجديد الاشتراك</a>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>
    
    <div class="container-fluid">
        <div class="row">
            <!-- الشريط الجانبي -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h5 class="text-primary fw-bold">
                            <i class="fas fa-hospital me-2"></i>
                            <?php echo htmlspecialchars($clinic['clinic_name']); ?>
                        </h5>
                        <small class="text-muted">مرحباً، <?php echo $_SESSION['full_name']; ?></small>
                        <br>
                        <span class="badge bg-<?php echo $clinic['subscription_plan'] == 'basic' ? 'info' : ($clinic['subscription_plan'] == 'pro' ? 'warning' : 'success'); ?>">
                            <?php echo $clinic['subscription_plan']; ?>
                        </span>
                    </div>
                    
                    <ul class="sidebar-nav">
                        <li>
                            <a href="dashboard.php" class="active">
                                <i class="fas fa-tachometer-alt"></i>
                                الرئيسية
                            </a>
                        </li>
                        <li>
                            <a href="patients.php">
                                <i class="fas fa-users"></i>
                                المرضى
                            </a>
                        </li>
                        <li>
                            <a href="appointments.php">
                                <i class="fas fa-calendar-alt"></i>
                                المواعيد
                            </a>
                        </li>
                        <li>
                            <a href="visits.php">
                                <i class="fas fa-stethoscope"></i>
                                الزيارات
                            </a>
                        </li>
                        <li>
                            <a href="prescriptions.php">
                                <i class="fas fa-prescription-bottle-alt"></i>
                                الوصفات
                            </a>
                        </li>
                        <li>
                            <a href="invoices.php">
                                <i class="fas fa-file-invoice-dollar"></i>
                                الفواتير
                            </a>
                        </li>
                        <li>
                            <a href="reports.php">
                                <i class="fas fa-chart-bar"></i>
                                التقارير
                            </a>
                        </li>
                        <li>
                            <a href="staff.php">
                                <i class="fas fa-user-md"></i>
                                الطاقم الطبي
                            </a>
                        </li>
                        <li>
                            <a href="settings.php">
                                <i class="fas fa-cog"></i>
                                الإعدادات
                            </a>
                        </li>
                        <li>
                            <a href="../public/login.php?logout=1" class="text-danger">
                                <i class="fas fa-sign-out-alt"></i>
                                تسجيل الخروج
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
            
            <!-- المحتوى الرئيسي -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-tachometer-alt me-2"></i>
                        لوحة التحكم
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-calendar me-1"></i>
                                <span class="current-time"></span>
                            </button>
                        </div>
                        <div class="btn-group">
                            <a href="appointments.php?action=add" class="btn btn-sm btn-primary">
                                <i class="fas fa-plus me-1"></i>
                                موعد جديد
                            </a>
                            <a href="patients.php?action=add" class="btn btn-sm btn-success">
                                <i class="fas fa-user-plus me-1"></i>
                                مريض جديد
                            </a>
                        </div>
                    </div>
                </div>
                
                <!-- بطاقات الإحصائيات الرئيسية -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="stats-card bg-primary text-white">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <div class="stats-number"><?php echo number_format($total_patients); ?></div>
                                    <div class="stats-label">إجمالي المرضى</div>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-users fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="stats-card bg-success text-white">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <div class="stats-number"><?php echo number_format($today_appointments); ?></div>
                                    <div class="stats-label">مواعيد اليوم</div>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-calendar-day fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="stats-card bg-info text-white">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <div class="stats-number"><?php echo format_currency($monthly_revenue); ?></div>
                                    <div class="stats-label">الإيرادات الشهرية</div>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-money-bill-wave fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="stats-card bg-warning text-white">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <div class="stats-number"><?php echo format_currency($pending_payments); ?></div>
                                    <div class="stats-label">مدفوعات معلقة</div>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-clock fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- الصف الثاني من الإحصائيات -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="stats-card">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <div class="stats-number text-primary"><?php echo number_format($monthly_visits); ?></div>
                                    <div class="stats-label">زيارات الشهر</div>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-stethoscope fa-2x text-primary opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="stats-card">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <div class="stats-number text-success"><?php echo number_format($new_patients); ?></div>
                                    <div class="stats-label">مرضى جدد</div>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-user-plus fa-2x text-success opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="stats-card">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <div class="stats-number text-info"><?php echo number_format($next_week_appointments); ?></div>
                                    <div class="stats-label">مواعيد الأسبوع</div>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-calendar-week fa-2x text-info opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="stats-card">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <div class="stats-number text-warning"><?php echo number_format($monthly_prescriptions); ?></div>
                                    <div class="stats-label">وصفات الشهر</div>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-prescription-bottle-alt fa-2x text-warning opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <!-- مواعيد اليوم -->
                    <div class="col-lg-8 mb-4">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-calendar-day me-2"></i>
                                    مواعيد اليوم
                                </h5>
                                <a href="appointments.php" class="btn btn-sm btn-outline-primary">
                                    عرض الكل
                                </a>
                            </div>
                            <div class="card-body">
                                <?php if (!empty($today_appointments_details)): ?>
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>الوقت</th>
                                                    <th>المريض</th>
                                                    <th>الطبيب</th>
                                                    <th>الحالة</th>
                                                    <th>الإجراءات</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($today_appointments_details as $appointment): ?>
                                                    <tr>
                                                        <td>
                                                            <strong><?php echo format_time_12hour($appointment['appointment_time']); ?></strong>
                                                        </td>
                                                        <td>
                                                            <?php echo htmlspecialchars($appointment['patient_name']); ?>
                                                            <br>
                                                            <small class="text-muted"><?php echo htmlspecialchars($appointment['patient_phone']); ?></small>
                                                        </td>
                                                        <td><?php echo htmlspecialchars($appointment['doctor_name']); ?></td>
                                                        <td>
                                                            <span class="badge bg-<?php 
                                                                echo $appointment['status'] == 'confirmed' ? 'success' : 
                                                                    ($appointment['status'] == 'completed' ? 'primary' : 
                                                                    ($appointment['status'] == 'cancelled' ? 'danger' : 'warning')); 
                                                            ?>">
                                                                <?php echo $text[$appointment['status']]; ?>
                                                            </span>
                                                        </td>
                                                        <td>
                                                            <div class="btn-group btn-group-sm">
                                                                <a href="visit.php?appointment_id=<?php echo $appointment['id']; ?>" class="btn btn-outline-primary">
                                                                    <i class="fas fa-stethoscope"></i>
                                                                </a>
                                                                <a href="appointments.php?action=edit&id=<?php echo $appointment['id']; ?>" class="btn btn-outline-secondary">
                                                                    <i class="fas fa-edit"></i>
                                                                </a>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php else: ?>
                                    <div class="text-center py-4">
                                        <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">لا توجد مواعيد اليوم</p>
                                        <a href="appointments.php?action=add" class="btn btn-primary">
                                            <i class="fas fa-plus me-2"></i>
                                            إضافة موعد جديد
                                        </a>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <!-- أحدث المرضى -->
                    <div class="col-lg-4 mb-4">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-users me-2"></i>
                                    أحدث المرضى
                                </h5>
                                <a href="patients.php" class="btn btn-sm btn-outline-primary">
                                    عرض الكل
                                </a>
                            </div>
                            <div class="card-body">
                                <?php if (!empty($recent_patients)): ?>
                                    <?php foreach ($recent_patients as $patient): ?>
                                        <div class="d-flex align-items-center mb-3">
                                            <div class="flex-shrink-0">
                                                <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                                    <i class="fas fa-user text-white"></i>
                                                </div>
                                            </div>
                                            <div class="flex-grow-1 ms-3">
                                                <h6 class="mb-0"><?php echo htmlspecialchars($patient['full_name']); ?></h6>
                                                <small class="text-muted">
                                                    <?php echo htmlspecialchars($patient['phone']); ?>
                                                    <br>
                                                    <?php echo format_date_arabic($patient['created_at']); ?>
                                                </small>
                                            </div>
                                            <div class="flex-shrink-0">
                                                <a href="patient_details.php?id=<?php echo $patient['id']; ?>" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <div class="text-center py-3">
                                        <i class="fas fa-user-plus fa-2x text-muted mb-2"></i>
                                        <p class="text-muted">لا توجد مرضى مسجلين</p>
                                        <a href="patients.php?action=add" class="btn btn-sm btn-primary">
                                            إضافة مريض
                                        </a>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="../assets/js/main.js"></script>
    
    <script>
        // تحديث الوقت الحالي
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('ar-EG', {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            document.querySelector('.current-time').textContent = timeString;
        }
        
        // تحديث الوقت كل ثانية
        setInterval(updateTime, 1000);
        updateTime();
        
        // تحديث البيانات كل 5 دقائق
        setInterval(function() {
            // يمكن إضافة AJAX لتحديث البيانات دون إعادة تحميل الصفحة
        }, 300000);
    </script>
</body>
</html>
