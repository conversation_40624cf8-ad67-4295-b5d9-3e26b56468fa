<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// تحديد اللغة
$lang = isset($_GET['lang']) ? $_GET['lang'] : (isset($_SESSION['lang']) ? $_SESSION['lang'] : 'ar');
$_SESSION['lang'] = $lang;
require_once "../lang/{$lang}.php";

// إعادة توجيه المستخدمين المسجلين
if (isset($_SESSION['user_id'])) {
    if ($_SESSION['user_type'] == 'admin') {
        header('Location: ../admin/dashboard.php');
    } else {
        header('Location: ../clinic/dashboard.php');
    }
    exit();
}

$error_message = '';
$success_message = '';

// معالجة تسجيل الدخول
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $username_email = sanitize_input($_POST['username_email']);
    $password = $_POST['password'];
    $remember_me = isset($_POST['remember_me']);
    
    if (empty($username_email) || empty($password)) {
        $error_message = 'يرجى ملء جميع الحقول المطلوبة';
    } else {
        try {
            // البحث في جدول الإدارة العامة أولاً
            $stmt = $pdo->prepare("SELECT id, username, email, password, user_type, full_name, is_active 
                                   FROM users 
                                   WHERE (username = ? OR email = ?) AND is_active = 1");
            $stmt->execute([$username_email, $username_email]);
            $admin_user = $stmt->fetch();
            
            if ($admin_user && verify_password($password, $admin_user['password'])) {
                // تسجيل دخول الإدارة العامة
                $_SESSION['user_id'] = $admin_user['id'];
                $_SESSION['username'] = $admin_user['username'];
                $_SESSION['email'] = $admin_user['email'];
                $_SESSION['full_name'] = $admin_user['full_name'];
                $_SESSION['user_type'] = $admin_user['user_type'];
                
                // تسجيل النشاط
                log_activity($pdo, $admin_user['id'], 'login', 'Admin login');
                
                // تذكر المستخدم
                if ($remember_me) {
                    setcookie('remember_token', base64_encode($admin_user['id'] . ':' . $admin_user['username']), time() + (30 * 24 * 60 * 60), '/');
                }
                
                header('Location: ../admin/dashboard.php');
                exit();
            } else {
                // البحث في جدول مستخدمي العيادات
                $stmt = $pdo->prepare("SELECT cu.*, c.clinic_name, c.is_active as clinic_active, c.subscription_end 
                                       FROM clinic_users cu 
                                       JOIN clinics c ON cu.clinic_id = c.id 
                                       WHERE (cu.username = ? OR cu.email = ?) AND cu.is_active = 1 AND c.is_active = 1");
                $stmt->execute([$username_email, $username_email]);
                $clinic_user = $stmt->fetch();
                
                if ($clinic_user && verify_password($password, $clinic_user['password'])) {
                    // التحقق من انتهاء الاشتراك
                    if ($clinic_user['subscription_end'] < date('Y-m-d')) {
                        $error_message = 'انتهت صلاحية اشتراك العيادة. يرجى تجديد الاشتراك.';
                    } else {
                        // تسجيل دخول مستخدم العيادة
                        $_SESSION['user_id'] = $clinic_user['id'];
                        $_SESSION['clinic_id'] = $clinic_user['clinic_id'];
                        $_SESSION['username'] = $clinic_user['username'];
                        $_SESSION['email'] = $clinic_user['email'];
                        $_SESSION['full_name'] = $clinic_user['full_name'];
                        $_SESSION['user_type'] = 'clinic_user';
                        $_SESSION['role'] = $clinic_user['role'];
                        $_SESSION['clinic_name'] = $clinic_user['clinic_name'];
                        
                        // تسجيل النشاط
                        log_activity($pdo, $clinic_user['id'], 'login', 'Clinic user login');
                        
                        // تذكر المستخدم
                        if ($remember_me) {
                            setcookie('remember_token', base64_encode($clinic_user['id'] . ':' . $clinic_user['username']), time() + (30 * 24 * 60 * 60), '/');
                        }
                        
                        header('Location: ../clinic/dashboard.php');
                        exit();
                    }
                } else {
                    $error_message = 'اسم المستخدم أو كلمة المرور غير صحيحة';
                }
            }
        } catch (Exception $e) {
            $error_message = 'حدث خطأ في النظام. يرجى المحاولة مرة أخرى.';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="<?php echo $lang; ?>" dir="<?php echo $lang == 'ar' ? 'rtl' : 'ltr'; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $text['login_title']; ?> - <?php echo $text['site_title']; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container-fluid">
        <div class="row min-vh-100">
            <!-- الجانب الأيسر - معلومات الموقع -->
            <div class="col-lg-6 bg-gradient-primary d-flex align-items-center justify-content-center">
                <div class="text-center text-white p-5">
                    <div class="mb-4">
                        <i class="fas fa-user-md" style="font-size: 5rem; opacity: 0.9;"></i>
                    </div>
                    <h1 class="display-4 fw-bold mb-3"><?php echo $text['site_title']; ?></h1>
                    <p class="lead mb-4"><?php echo $text['tagline']; ?></p>
                    <div class="row text-center">
                        <div class="col-4">
                            <i class="fas fa-users fa-2x mb-2"></i>
                            <p class="small">إدارة المرضى</p>
                        </div>
                        <div class="col-4">
                            <i class="fas fa-calendar-alt fa-2x mb-2"></i>
                            <p class="small">نظام المواعيد</p>
                        </div>
                        <div class="col-4">
                            <i class="fas fa-file-invoice fa-2x mb-2"></i>
                            <p class="small">الفواتير</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- الجانب الأيمن - نموذج تسجيل الدخول -->
            <div class="col-lg-6 d-flex align-items-center justify-content-center">
                <div class="w-100" style="max-width: 400px;">
                    <div class="card shadow-lg border-0">
                        <div class="card-body p-5">
                            <div class="text-center mb-4">
                                <h2 class="fw-bold text-primary"><?php echo $text['login_title']; ?></h2>
                                <p class="text-muted"><?php echo $text['welcome_back']; ?></p>
                            </div>
                            
                            <?php if ($error_message): ?>
                                <div class="alert alert-danger">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <?php echo $error_message; ?>
                                </div>
                            <?php endif; ?>
                            
                            <?php if ($success_message): ?>
                                <div class="alert alert-success">
                                    <i class="fas fa-check-circle me-2"></i>
                                    <?php echo $success_message; ?>
                                </div>
                            <?php endif; ?>
                            
                            <form method="POST" class="needs-validation" novalidate>
                                <div class="mb-3">
                                    <label for="username_email" class="form-label">
                                        <i class="fas fa-user me-2"></i>
                                        <?php echo $text['username_email']; ?>
                                    </label>
                                    <input type="text" class="form-control form-control-lg" id="username_email" 
                                           name="username_email" required 
                                           value="<?php echo isset($_POST['username_email']) ? htmlspecialchars($_POST['username_email']) : ''; ?>">
                                    <div class="invalid-feedback">
                                        يرجى إدخال اسم المستخدم أو البريد الإلكتروني
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="password" class="form-label">
                                        <i class="fas fa-lock me-2"></i>
                                        <?php echo $text['password']; ?>
                                    </label>
                                    <div class="input-group">
                                        <input type="password" class="form-control form-control-lg" id="password" 
                                               name="password" required>
                                        <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                    <div class="invalid-feedback">
                                        يرجى إدخال كلمة المرور
                                    </div>
                                </div>
                                
                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="remember_me" name="remember_me">
                                    <label class="form-check-label" for="remember_me">
                                        <?php echo $text['remember_me']; ?>
                                    </label>
                                </div>
                                
                                <div class="d-grid mb-3">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-sign-in-alt me-2"></i>
                                        <?php echo $text['login_button']; ?>
                                    </button>
                                </div>
                                
                                <div class="text-center">
                                    <a href="forgot_password.php" class="text-decoration-none">
                                        <?php echo $text['forgot_password']; ?>
                                    </a>
                                </div>
                            </form>
                            
                            <hr class="my-4">
                            
                            <div class="text-center">
                                <p class="mb-2"><?php echo $text['no_account']; ?></p>
                                <a href="register.php" class="btn btn-outline-primary">
                                    <i class="fas fa-user-plus me-2"></i>
                                    <?php echo $text['create_account']; ?>
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- تغيير اللغة -->
                    <div class="text-center mt-3">
                        <div class="btn-group" role="group">
                            <a href="?lang=ar" class="btn btn-sm <?php echo $lang == 'ar' ? 'btn-primary' : 'btn-outline-primary'; ?>">
                                العربية
                            </a>
                            <a href="?lang=en" class="btn btn-sm <?php echo $lang == 'en' ? 'btn-primary' : 'btn-outline-primary'; ?>">
                                English
                            </a>
                        </div>
                    </div>
                    
                    <!-- رابط العودة للصفحة الرئيسية -->
                    <div class="text-center mt-3">
                        <a href="../index.php" class="text-muted text-decoration-none">
                            <i class="fas fa-arrow-left me-2"></i>
                            العودة للصفحة الرئيسية
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="../assets/js/main.js"></script>
    
    <script>
        // إظهار/إخفاء كلمة المرور
        document.getElementById('togglePassword').addEventListener('click', function() {
            const password = document.getElementById('password');
            const icon = this.querySelector('i');
            
            if (password.type === 'password') {
                password.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                password.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });
        
        // التركيز على حقل اسم المستخدم عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('username_email').focus();
        });
    </script>
</body>
</html>
