/* ملف CSS الرئيسي لموقع حكيم */

/* الخطوط */
body {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
}

/* الألوان الأساسية */
:root {
    --primary-color: #2c5aa0;
    --secondary-color: #34d399;
    --accent-color: #f59e0b;
    --danger-color: #ef4444;
    --warning-color: #f59e0b;
    --success-color: #10b981;
    --info-color: #3b82f6;
    --light-color: #f8fafc;
    --dark-color: #1e293b;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;
}

/* تدرج الألوان */
.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, #1e40af 100%);
}

.bg-gradient-secondary {
    background: linear-gradient(135deg, var(--secondary-color) 0%, #059669 100%);
}

/* النافبار */
.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    padding: 0.5rem 1rem;
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: var(--primary-color) !important;
}

/* القسم الرئيسي */
.hero-section {
    min-height: 80vh;
    display: flex;
    align-items: center;
}

.min-vh-75 {
    min-height: 75vh;
}

/* بطاقات المميزات */
.feature-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: 1px solid var(--gray-200);
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.feature-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--primary-color), #1e40af);
    border-radius: 50%;
    color: white;
}

/* الأزرار */
.btn {
    border-radius: 8px;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    transition: all 0.3s ease;
}

.btn-primary {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background: #1e40af;
    border-color: #1e40af;
    transform: translateY(-2px);
}

.btn-success {
    background: var(--success-color);
    border-color: var(--success-color);
}

.btn-warning {
    background: var(--warning-color);
    border-color: var(--warning-color);
}

.btn-danger {
    background: var(--danger-color);
    border-color: var(--danger-color);
}

/* النماذج */
.form-control {
    border-radius: 8px;
    border: 2px solid var(--gray-200);
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(44, 90, 160, 0.25);
}

.form-label {
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: 0.5rem;
}

/* البطاقات */
.card {
    border-radius: 15px;
    border: 1px solid var(--gray-200);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.card-header {
    background: var(--gray-100);
    border-bottom: 1px solid var(--gray-200);
    border-radius: 15px 15px 0 0 !important;
    padding: 1.25rem;
}

.card-body {
    padding: 1.5rem;
}

/* الجداول */
.table {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.table thead th {
    background: var(--primary-color);
    color: white;
    font-weight: 600;
    border: none;
    padding: 1rem;
}

.table tbody td {
    padding: 1rem;
    vertical-align: middle;
    border-bottom: 1px solid var(--gray-200);
}

.table tbody tr:hover {
    background: var(--gray-100);
}

/* الشارات */
.badge {
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
}

.badge-success {
    background: var(--success-color);
}

.badge-warning {
    background: var(--warning-color);
}

.badge-danger {
    background: var(--danger-color);
}

.badge-info {
    background: var(--info-color);
}

/* الإحصائيات */
.stats-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    border-left: 4px solid var(--primary-color);
    transition: all 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.stats-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

.stats-label {
    color: var(--gray-600);
    font-weight: 600;
}

/* الشريط الجانبي */
.sidebar {
    background: white;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
    min-height: 100vh;
}

.sidebar-nav {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sidebar-nav li {
    margin-bottom: 0.5rem;
}

.sidebar-nav a {
    display: block;
    padding: 1rem 1.5rem;
    color: var(--gray-700);
    text-decoration: none;
    border-radius: 8px;
    margin: 0 1rem;
    transition: all 0.3s ease;
}

.sidebar-nav a:hover,
.sidebar-nav a.active {
    background: var(--primary-color);
    color: white;
}

.sidebar-nav i {
    width: 20px;
    margin-left: 10px;
}

/* التقويم */
.calendar {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.calendar-header {
    background: var(--primary-color);
    color: white;
    padding: 1rem;
    border-radius: 15px 15px 0 0;
    text-align: center;
    font-weight: 600;
}

.calendar-day {
    padding: 1rem;
    border: 1px solid var(--gray-200);
    min-height: 100px;
    position: relative;
}

.calendar-day.today {
    background: var(--light-color);
    border-color: var(--primary-color);
}

.appointment-item {
    background: var(--primary-color);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    margin-bottom: 0.25rem;
}

/* الرسائل والتنبيهات */
.alert {
    border-radius: 10px;
    border: none;
    padding: 1rem 1.5rem;
    margin-bottom: 1rem;
}

.alert-success {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
    border-left: 4px solid var(--success-color);
}

.alert-danger {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
    border-left: 4px solid var(--danger-color);
}

.alert-warning {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
    border-left: 4px solid var(--warning-color);
}

.alert-info {
    background: rgba(59, 130, 246, 0.1);
    color: var(--info-color);
    border-left: 4px solid var(--info-color);
}

/* التحميل */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* الطباعة */
@media print {
    .no-print {
        display: none !important;
    }
    
    .print-only {
        display: block !important;
    }
    
    body {
        font-size: 12pt;
        line-height: 1.4;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #ddd;
    }
}

/* الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .hero-section {
        text-align: center;
        padding: 2rem 0;
    }
    
    .display-4 {
        font-size: 2rem;
    }
    
    .stats-card {
        margin-bottom: 1rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .sidebar {
        position: fixed;
        top: 0;
        right: -100%;
        width: 280px;
        height: 100vh;
        z-index: 1050;
        transition: right 0.3s ease;
    }
    
    .sidebar.show {
        right: 0;
    }
}

/* اللغة العربية */
[dir="rtl"] {
    text-align: right;
}

[dir="rtl"] .sidebar-nav i {
    margin-right: 10px;
    margin-left: 0;
}

[dir="rtl"] .dropdown-menu {
    right: 0;
    left: auto;
}

/* تحسينات إضافية */
.text-primary {
    color: var(--primary-color) !important;
}

.bg-primary {
    background-color: var(--primary-color) !important;
}

.border-primary {
    border-color: var(--primary-color) !important;
}

.shadow-sm {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05) !important;
}

.shadow {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
}

.shadow-lg {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15) !important;
}
