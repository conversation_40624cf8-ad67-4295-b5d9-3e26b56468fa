<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول
require_login();
require_clinic_access($pdo, $_SESSION['clinic_id']);

// تحديد اللغة
$lang = isset($_SESSION['lang']) ? $_SESSION['lang'] : 'ar';
require_once "../lang/{$lang}.php";

$clinic_id = $_SESSION['clinic_id'];
$action = isset($_GET['action']) ? $_GET['action'] : 'list';
$patient_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

$error_message = '';
$success_message = '';

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if ($action == 'add' || $action == 'edit') {
        $full_name = sanitize_input($_POST['full_name']);
        $phone = sanitize_input($_POST['phone']);
        $email = sanitize_input($_POST['email']);
        $date_of_birth = $_POST['date_of_birth'];
        $gender = $_POST['gender'];
        $address = sanitize_input($_POST['address']);
        $emergency_contact = sanitize_input($_POST['emergency_contact']);
        $emergency_phone = sanitize_input($_POST['emergency_phone']);
        $blood_type = sanitize_input($_POST['blood_type']);
        $allergies = sanitize_input($_POST['allergies']);
        $chronic_diseases = sanitize_input($_POST['chronic_diseases']);
        $notes = sanitize_input($_POST['notes']);
        
        if (empty($full_name) || empty($phone) || empty($gender)) {
            $error_message = 'يرجى ملء الحقول المطلوبة';
        } else {
            try {
                if ($action == 'add') {
                    // إنشاء رقم مريض فريد
                    $patient_number = generate_patient_number($clinic_id);
                    
                    $stmt = $pdo->prepare("INSERT INTO patients (clinic_id, patient_number, full_name, phone, email, date_of_birth, gender, address, emergency_contact, emergency_phone, blood_type, allergies, chronic_diseases, notes, created_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
                    $stmt->execute([$clinic_id, $patient_number, $full_name, $phone, $email, $date_of_birth, $gender, $address, $emergency_contact, $emergency_phone, $blood_type, $allergies, $chronic_diseases, $notes, $_SESSION['user_id']]);
                    
                    $success_message = 'تم إضافة المريض بنجاح';
                    log_activity($pdo, $_SESSION['user_id'], 'add_patient', "Added patient: $full_name");
                } else {
                    $stmt = $pdo->prepare("UPDATE patients SET full_name = ?, phone = ?, email = ?, date_of_birth = ?, gender = ?, address = ?, emergency_contact = ?, emergency_phone = ?, blood_type = ?, allergies = ?, chronic_diseases = ?, notes = ? WHERE id = ? AND clinic_id = ?");
                    $stmt->execute([$full_name, $phone, $email, $date_of_birth, $gender, $address, $emergency_contact, $emergency_phone, $blood_type, $allergies, $chronic_diseases, $notes, $patient_id, $clinic_id]);
                    
                    $success_message = 'تم تحديث بيانات المريض بنجاح';
                    log_activity($pdo, $_SESSION['user_id'], 'edit_patient', "Updated patient: $full_name");
                }
                
                $action = 'list';
            } catch (Exception $e) {
                $error_message = 'حدث خطأ في النظام';
            }
        }
    }
}

// حذف مريض
if ($action == 'delete' && $patient_id > 0) {
    try {
        $stmt = $pdo->prepare("DELETE FROM patients WHERE id = ? AND clinic_id = ?");
        $stmt->execute([$patient_id, $clinic_id]);
        
        $success_message = 'تم حذف المريض بنجاح';
        log_activity($pdo, $_SESSION['user_id'], 'delete_patient', "Deleted patient ID: $patient_id");
        $action = 'list';
    } catch (Exception $e) {
        $error_message = 'حدث خطأ في حذف المريض';
    }
}

// جلب بيانات المريض للتعديل
$patient = null;
if ($action == 'edit' && $patient_id > 0) {
    $stmt = $pdo->prepare("SELECT * FROM patients WHERE id = ? AND clinic_id = ?");
    $stmt->execute([$patient_id, $clinic_id]);
    $patient = $stmt->fetch();
    
    if (!$patient) {
        $error_message = 'المريض غير موجود';
        $action = 'list';
    }
}

// جلب قائمة المرضى
$patients = [];
$search = isset($_GET['search']) ? sanitize_input($_GET['search']) : '';
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$per_page = 20;
$offset = ($page - 1) * $per_page;

if ($action == 'list') {
    try {
        $where_clause = "WHERE clinic_id = ?";
        $params = [$clinic_id];
        
        if (!empty($search)) {
            $where_clause .= " AND (full_name LIKE ? OR phone LIKE ? OR patient_number LIKE ?)";
            $search_term = "%$search%";
            $params[] = $search_term;
            $params[] = $search_term;
            $params[] = $search_term;
        }
        
        // عدد المرضى الإجمالي
        $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM patients $where_clause");
        $stmt->execute($params);
        $total_patients = $stmt->fetch()['total'];
        $total_pages = ceil($total_patients / $per_page);
        
        // جلب المرضى
        $stmt = $pdo->prepare("SELECT * FROM patients $where_clause ORDER BY created_at DESC LIMIT $per_page OFFSET $offset");
        $stmt->execute($params);
        $patients = $stmt->fetchAll();
        
    } catch (Exception $e) {
        $error_message = 'حدث خطأ في جلب البيانات';
    }
}
?>
<!DOCTYPE html>
<html lang="<?php echo $lang; ?>" dir="<?php echo $lang == 'ar' ? 'rtl' : 'ltr'; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المرضى - <?php echo $text['site_title']; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container-fluid">
        <div class="row">
            <!-- الشريط الجانبي -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h5 class="text-primary fw-bold">
                            <i class="fas fa-hospital me-2"></i>
                            <?php echo $_SESSION['clinic_name']; ?>
                        </h5>
                        <small class="text-muted"><?php echo $_SESSION['full_name']; ?></small>
                    </div>
                    
                    <ul class="sidebar-nav">
                        <li><a href="dashboard.php"><i class="fas fa-tachometer-alt"></i>الرئيسية</a></li>
                        <li><a href="patients.php" class="active"><i class="fas fa-users"></i>المرضى</a></li>
                        <li><a href="appointments.php"><i class="fas fa-calendar-alt"></i>المواعيد</a></li>
                        <li><a href="visits.php"><i class="fas fa-stethoscope"></i>الزيارات</a></li>
                        <li><a href="prescriptions.php"><i class="fas fa-prescription-bottle-alt"></i>الوصفات</a></li>
                        <li><a href="invoices.php"><i class="fas fa-file-invoice-dollar"></i>الفواتير</a></li>
                        <li><a href="reports.php"><i class="fas fa-chart-bar"></i>التقارير</a></li>
                        <li><a href="settings.php"><i class="fas fa-cog"></i>الإعدادات</a></li>
                        <li><a href="../public/login.php?logout=1" class="text-danger"><i class="fas fa-sign-out-alt"></i>تسجيل الخروج</a></li>
                    </ul>
                </div>
            </nav>
            
            <!-- المحتوى الرئيسي -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-users me-2"></i>
                        <?php echo $action == 'add' ? 'إضافة مريض جديد' : ($action == 'edit' ? 'تعديل بيانات المريض' : 'إدارة المرضى'); ?>
                    </h1>
                    
                    <?php if ($action == 'list'): ?>
                        <div class="btn-toolbar mb-2 mb-md-0">
                            <div class="btn-group me-2">
                                <button type="button" class="btn btn-sm btn-outline-secondary btn-export" data-table="patientsTable" data-filename="patients.csv">
                                    <i class="fas fa-download me-1"></i>
                                    تصدير
                                </button>
                            </div>
                            <a href="patients.php?action=add" class="btn btn-sm btn-primary">
                                <i class="fas fa-plus me-1"></i>
                                إضافة مريض جديد
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
                
                <!-- الرسائل -->
                <?php if ($error_message): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?php echo $error_message; ?>
                    </div>
                <?php endif; ?>
                
                <?php if ($success_message): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        <?php echo $success_message; ?>
                    </div>
                <?php endif; ?>
                
                <?php if ($action == 'list'): ?>
                    <!-- البحث والفلترة -->
                    <div class="card mb-4">
                        <div class="card-body">
                            <form method="GET" class="row g-3">
                                <div class="col-md-6">
                                    <label for="search" class="form-label">البحث</label>
                                    <input type="text" class="form-control" id="search" name="search" 
                                           placeholder="البحث بالاسم أو الهاتف أو رقم المريض" 
                                           value="<?php echo htmlspecialchars($search); ?>">
                                </div>
                                <div class="col-md-6 d-flex align-items-end">
                                    <button type="submit" class="btn btn-primary me-2">
                                        <i class="fas fa-search me-1"></i>
                                        بحث
                                    </button>
                                    <a href="patients.php" class="btn btn-outline-secondary">
                                        <i class="fas fa-times me-1"></i>
                                        مسح
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                    
                    <!-- جدول المرضى -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                قائمة المرضى (<?php echo number_format($total_patients); ?> مريض)
                            </h5>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($patients)): ?>
                                <div class="table-responsive">
                                    <table class="table table-hover" id="patientsTable">
                                        <thead>
                                            <tr>
                                                <th>رقم المريض</th>
                                                <th>الاسم</th>
                                                <th>الهاتف</th>
                                                <th>العمر</th>
                                                <th>الجنس</th>
                                                <th>تاريخ التسجيل</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($patients as $p): ?>
                                                <tr>
                                                    <td><strong><?php echo htmlspecialchars($p['patient_number']); ?></strong></td>
                                                    <td><?php echo htmlspecialchars($p['full_name']); ?></td>
                                                    <td><?php echo htmlspecialchars($p['phone']); ?></td>
                                                    <td>
                                                        <?php 
                                                        if ($p['date_of_birth']) {
                                                            echo calculate_age($p['date_of_birth']) . ' سنة';
                                                        } else {
                                                            echo '-';
                                                        }
                                                        ?>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-<?php echo $p['gender'] == 'male' ? 'primary' : 'pink'; ?>">
                                                            <?php echo $p['gender'] == 'male' ? 'ذكر' : 'أنثى'; ?>
                                                        </span>
                                                    </td>
                                                    <td><?php echo format_date_arabic($p['created_at']); ?></td>
                                                    <td>
                                                        <div class="btn-group btn-group-sm">
                                                            <a href="patient_details.php?id=<?php echo $p['id']; ?>" class="btn btn-outline-info" title="عرض التفاصيل">
                                                                <i class="fas fa-eye"></i>
                                                            </a>
                                                            <a href="patients.php?action=edit&id=<?php echo $p['id']; ?>" class="btn btn-outline-primary" title="تعديل">
                                                                <i class="fas fa-edit"></i>
                                                            </a>
                                                            <a href="appointments.php?action=add&patient_id=<?php echo $p['id']; ?>" class="btn btn-outline-success" title="حجز موعد">
                                                                <i class="fas fa-calendar-plus"></i>
                                                            </a>
                                                            <a href="patients.php?action=delete&id=<?php echo $p['id']; ?>" class="btn btn-outline-danger btn-delete" title="حذف">
                                                                <i class="fas fa-trash"></i>
                                                            </a>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                                
                                <!-- الترقيم -->
                                <?php if ($total_pages > 1): ?>
                                    <nav aria-label="Patient pagination">
                                        <ul class="pagination justify-content-center">
                                            <?php if ($page > 1): ?>
                                                <li class="page-item">
                                                    <a class="page-link" href="?page=<?php echo $page - 1; ?>&search=<?php echo urlencode($search); ?>">السابق</a>
                                                </li>
                                            <?php endif; ?>
                                            
                                            <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                                                <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                                    <a class="page-link" href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>"><?php echo $i; ?></a>
                                                </li>
                                            <?php endfor; ?>
                                            
                                            <?php if ($page < $total_pages): ?>
                                                <li class="page-item">
                                                    <a class="page-link" href="?page=<?php echo $page + 1; ?>&search=<?php echo urlencode($search); ?>">التالي</a>
                                                </li>
                                            <?php endif; ?>
                                        </ul>
                                    </nav>
                                <?php endif; ?>
                            <?php else: ?>
                                <div class="text-center py-5">
                                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">لا توجد مرضى مسجلين</h5>
                                    <p class="text-muted">ابدأ بإضافة أول مريض لك</p>
                                    <a href="patients.php?action=add" class="btn btn-primary">
                                        <i class="fas fa-plus me-2"></i>
                                        إضافة مريض جديد
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                <?php elseif ($action == 'add' || $action == 'edit'): ?>
                    <!-- نموذج إضافة/تعديل مريض -->
                    <div class="card">
                        <div class="card-body">
                            <form method="POST" class="needs-validation" novalidate>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="full_name" class="form-label">
                                            الاسم الكامل <span class="text-danger">*</span>
                                        </label>
                                        <input type="text" class="form-control" id="full_name" name="full_name" required
                                               value="<?php echo $patient ? htmlspecialchars($patient['full_name']) : ''; ?>">
                                        <div class="invalid-feedback">يرجى إدخال الاسم الكامل</div>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="phone" class="form-label">
                                            رقم الهاتف <span class="text-danger">*</span>
                                        </label>
                                        <input type="tel" class="form-control" id="phone" name="phone" required
                                               value="<?php echo $patient ? htmlspecialchars($patient['phone']) : ''; ?>">
                                        <div class="invalid-feedback">يرجى إدخال رقم الهاتف</div>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="email" class="form-label">البريد الإلكتروني</label>
                                        <input type="email" class="form-control" id="email" name="email"
                                               value="<?php echo $patient ? htmlspecialchars($patient['email']) : ''; ?>">
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="date_of_birth" class="form-label">تاريخ الميلاد</label>
                                        <input type="date" class="form-control" id="date_of_birth" name="date_of_birth"
                                               value="<?php echo $patient ? $patient['date_of_birth'] : ''; ?>">
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="gender" class="form-label">
                                            الجنس <span class="text-danger">*</span>
                                        </label>
                                        <select class="form-select" id="gender" name="gender" required>
                                            <option value="">اختر الجنس</option>
                                            <option value="male" <?php echo ($patient && $patient['gender'] == 'male') ? 'selected' : ''; ?>>ذكر</option>
                                            <option value="female" <?php echo ($patient && $patient['gender'] == 'female') ? 'selected' : ''; ?>>أنثى</option>
                                        </select>
                                        <div class="invalid-feedback">يرجى اختيار الجنس</div>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="blood_type" class="form-label">فصيلة الدم</label>
                                        <select class="form-select" id="blood_type" name="blood_type">
                                            <option value="">اختر فصيلة الدم</option>
                                            <option value="A+" <?php echo ($patient && $patient['blood_type'] == 'A+') ? 'selected' : ''; ?>>A+</option>
                                            <option value="A-" <?php echo ($patient && $patient['blood_type'] == 'A-') ? 'selected' : ''; ?>>A-</option>
                                            <option value="B+" <?php echo ($patient && $patient['blood_type'] == 'B+') ? 'selected' : ''; ?>>B+</option>
                                            <option value="B-" <?php echo ($patient && $patient['blood_type'] == 'B-') ? 'selected' : ''; ?>>B-</option>
                                            <option value="AB+" <?php echo ($patient && $patient['blood_type'] == 'AB+') ? 'selected' : ''; ?>>AB+</option>
                                            <option value="AB-" <?php echo ($patient && $patient['blood_type'] == 'AB-') ? 'selected' : ''; ?>>AB-</option>
                                            <option value="O+" <?php echo ($patient && $patient['blood_type'] == 'O+') ? 'selected' : ''; ?>>O+</option>
                                            <option value="O-" <?php echo ($patient && $patient['blood_type'] == 'O-') ? 'selected' : ''; ?>>O-</option>
                                        </select>
                                    </div>
                                    
                                    <div class="col-12 mb-3">
                                        <label for="address" class="form-label">العنوان</label>
                                        <textarea class="form-control" id="address" name="address" rows="2"><?php echo $patient ? htmlspecialchars($patient['address']) : ''; ?></textarea>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="emergency_contact" class="form-label">جهة الاتصال في الطوارئ</label>
                                        <input type="text" class="form-control" id="emergency_contact" name="emergency_contact"
                                               value="<?php echo $patient ? htmlspecialchars($patient['emergency_contact']) : ''; ?>">
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="emergency_phone" class="form-label">هاتف الطوارئ</label>
                                        <input type="tel" class="form-control" id="emergency_phone" name="emergency_phone"
                                               value="<?php echo $patient ? htmlspecialchars($patient['emergency_phone']) : ''; ?>">
                                    </div>
                                    
                                    <div class="col-12 mb-3">
                                        <label for="allergies" class="form-label">الحساسية</label>
                                        <textarea class="form-control" id="allergies" name="allergies" rows="2" placeholder="اذكر أي حساسية معروفة"><?php echo $patient ? htmlspecialchars($patient['allergies']) : ''; ?></textarea>
                                    </div>
                                    
                                    <div class="col-12 mb-3">
                                        <label for="chronic_diseases" class="form-label">الأمراض المزمنة</label>
                                        <textarea class="form-control" id="chronic_diseases" name="chronic_diseases" rows="2" placeholder="اذكر أي أمراض مزمنة"><?php echo $patient ? htmlspecialchars($patient['chronic_diseases']) : ''; ?></textarea>
                                    </div>
                                    
                                    <div class="col-12 mb-3">
                                        <label for="notes" class="form-label">ملاحظات إضافية</label>
                                        <textarea class="form-control" id="notes" name="notes" rows="3"><?php echo $patient ? htmlspecialchars($patient['notes']) : ''; ?></textarea>
                                    </div>
                                </div>
                                
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>
                                        <?php echo $action == 'add' ? 'إضافة المريض' : 'حفظ التغييرات'; ?>
                                    </button>
                                    <a href="patients.php" class="btn btn-secondary">
                                        <i class="fas fa-times me-2"></i>
                                        إلغاء
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                <?php endif; ?>
            </main>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="../assets/js/main.js"></script>
</body>
</html>
