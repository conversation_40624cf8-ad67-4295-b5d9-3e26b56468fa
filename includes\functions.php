<?php
// ملف الوظائف المساعدة

// تنظيف البيانات المدخلة
function sanitize_input($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

// التحقق من صحة البريد الإلكتروني
function validate_email($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

// التحقق من قوة كلمة المرور
function validate_password($password) {
    return strlen($password) >= 6;
}

// تشفير كلمة المرور
function hash_password($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

// التحقق من كلمة المرور
function verify_password($password, $hash) {
    return password_verify($password, $hash);
}

// إنشاء رقم عشوائي
function generate_random_number($length = 6) {
    return str_pad(mt_rand(1, pow(10, $length) - 1), $length, '0', STR_PAD_LEFT);
}

// إنشاء رقم مريض فريد
function generate_patient_number($clinic_id) {
    return 'P' . $clinic_id . date('Y') . generate_random_number(4);
}

// إنشاء رقم وصفة فريد
function generate_prescription_number($clinic_id) {
    return 'RX' . $clinic_id . date('Ymd') . generate_random_number(3);
}

// إنشاء رقم فاتورة فريد
function generate_invoice_number($clinic_id) {
    return 'INV' . $clinic_id . date('Ymd') . generate_random_number(3);
}

// تحويل التاريخ إلى التنسيق العربي
function format_date_arabic($date) {
    $months = [
        '01' => 'يناير', '02' => 'فبراير', '03' => 'مارس', '04' => 'أبريل',
        '05' => 'مايو', '06' => 'يونيو', '07' => 'يوليو', '08' => 'أغسطس',
        '09' => 'سبتمبر', '10' => 'أكتوبر', '11' => 'نوفمبر', '12' => 'ديسمبر'
    ];
    
    $date_parts = explode('-', $date);
    if (count($date_parts) == 3) {
        return $date_parts[2] . ' ' . $months[$date_parts[1]] . ' ' . $date_parts[0];
    }
    return $date;
}

// تحويل الوقت إلى تنسيق 12 ساعة
function format_time_12hour($time) {
    return date('g:i A', strtotime($time));
}

// حساب العمر من تاريخ الميلاد
function calculate_age($birth_date) {
    $birth = new DateTime($birth_date);
    $today = new DateTime();
    $age = $birth->diff($today);
    return $age->y;
}

// التحقق من صلاحية المستخدم
function check_user_permission($required_role, $user_role) {
    $roles_hierarchy = [
        'secretary' => 1,
        'nurse' => 2,
        'doctor' => 3,
        'owner' => 4,
        'admin' => 5,
        'super_admin' => 6
    ];
    
    return $roles_hierarchy[$user_role] >= $roles_hierarchy[$required_role];
}

// إرسال إشعار بريد إلكتروني
function send_email_notification($to, $subject, $message) {
    $headers = "From: <EMAIL>\r\n";
    $headers .= "Reply-To: <EMAIL>\r\n";
    $headers .= "Content-Type: text/html; charset=UTF-8\r\n";
    
    return mail($to, $subject, $message, $headers);
}

// إرسال رسالة SMS (يحتاج إلى تكامل مع خدمة SMS)
function send_sms_notification($phone, $message) {
    // هنا يمكن إضافة تكامل مع خدمة SMS
    // مثل Twilio أو أي خدمة أخرى
    return true;
}

// تحميل ملف بشكل آمن
function upload_file($file, $upload_dir, $allowed_types = ['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx']) {
    if (!isset($file['error']) || is_array($file['error'])) {
        return ['success' => false, 'message' => 'خطأ في رفع الملف'];
    }
    
    if ($file['error'] !== UPLOAD_ERR_OK) {
        return ['success' => false, 'message' => 'خطأ في رفع الملف'];
    }
    
    if ($file['size'] > 5 * 1024 * 1024) { // 5MB
        return ['success' => false, 'message' => 'حجم الملف كبير جداً'];
    }
    
    $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    if (!in_array($file_extension, $allowed_types)) {
        return ['success' => false, 'message' => 'نوع الملف غير مسموح'];
    }
    
    $new_filename = uniqid() . '.' . $file_extension;
    $upload_path = $upload_dir . '/' . $new_filename;
    
    if (!is_dir($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }
    
    if (move_uploaded_file($file['tmp_name'], $upload_path)) {
        return [
            'success' => true,
            'filename' => $new_filename,
            'path' => $upload_path,
            'size' => $file['size']
        ];
    }
    
    return ['success' => false, 'message' => 'فشل في رفع الملف'];
}

// تسجيل الأنشطة
function log_activity($pdo, $user_id, $action, $details = '') {
    try {
        $stmt = $pdo->prepare("INSERT INTO activity_logs (user_id, action, details, ip_address, created_at) VALUES (?, ?, ?, ?, NOW())");
        $stmt->execute([$user_id, $action, $details, $_SERVER['REMOTE_ADDR']]);
        return true;
    } catch (Exception $e) {
        return false;
    }
}

// التحقق من انتهاء الاشتراك
function check_subscription_status($pdo, $clinic_id) {
    try {
        $stmt = $pdo->prepare("SELECT subscription_end, subscription_plan FROM clinics WHERE id = ?");
        $stmt->execute([$clinic_id]);
        $clinic = $stmt->fetch();
        
        if (!$clinic) {
            return ['status' => 'invalid', 'message' => 'عيادة غير موجودة'];
        }
        
        $today = date('Y-m-d');
        $subscription_end = $clinic['subscription_end'];
        
        if ($subscription_end < $today) {
            return ['status' => 'expired', 'message' => 'انتهت صلاحية الاشتراك'];
        }
        
        $days_left = (strtotime($subscription_end) - strtotime($today)) / (60 * 60 * 24);
        
        if ($days_left <= 7) {
            return ['status' => 'expiring', 'message' => 'سينتهي الاشتراك خلال ' . ceil($days_left) . ' أيام'];
        }
        
        return ['status' => 'active', 'message' => 'الاشتراك نشط'];
        
    } catch (Exception $e) {
        return ['status' => 'error', 'message' => 'خطأ في التحقق من الاشتراك'];
    }
}

// إنشاء نسخة احتياطية من قاعدة البيانات
function backup_database($pdo, $backup_dir) {
    try {
        $backup_file = $backup_dir . '/backup_' . date('Y-m-d_H-i-s') . '.sql';
        
        if (!is_dir($backup_dir)) {
            mkdir($backup_dir, 0755, true);
        }
        
        // هنا يمكن إضافة كود إنشاء النسخة الاحتياطية
        // باستخدام mysqldump أو طرق أخرى
        
        return ['success' => true, 'file' => $backup_file];
    } catch (Exception $e) {
        return ['success' => false, 'message' => $e->getMessage()];
    }
}

// تنسيق الأرقام للعملة
function format_currency($amount, $currency = 'ج.م') {
    return number_format($amount, 2) . ' ' . $currency;
}

// تحويل الأرقام إلى كلمات (للفواتير)
function number_to_words($number) {
    // يمكن إضافة دالة تحويل الأرقام إلى كلمات باللغة العربية
    return $number;
}

// التحقق من CSRF Token
function verify_csrf_token($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

// إنشاء CSRF Token
function generate_csrf_token() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

// تنظيف الجلسة
function clean_session() {
    session_unset();
    session_destroy();
    session_start();
    session_regenerate_id(true);
}

// إعادة توجيه آمنة
function safe_redirect($url) {
    header('Location: ' . $url);
    exit();
}

// التحقق من تسجيل الدخول
function require_login() {
    if (!isset($_SESSION['user_id'])) {
        safe_redirect('../public/login.php');
    }
}

// التحقق من صلاحية العيادة
function require_clinic_access($pdo, $clinic_id) {
    if (!isset($_SESSION['clinic_id']) || $_SESSION['clinic_id'] != $clinic_id) {
        safe_redirect('../public/login.php');
    }
    
    // التحقق من حالة الاشتراك
    $subscription = check_subscription_status($pdo, $clinic_id);
    if ($subscription['status'] == 'expired') {
        safe_redirect('../public/subscription_expired.php');
    }
}
?>
