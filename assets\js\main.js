// ملف JavaScript الرئيسي لموقع حكيم

// تشغيل الكود عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تهيئة التولتيبس
    initializeTooltips();
    
    // تهيئة المودالات
    initializeModals();
    
    // تهيئة النماذج
    initializeForms();
    
    // تهيئة الجداول
    initializeTables();
    
    // تهيئة التقويم
    initializeCalendar();
    
    // تهيئة الإشعارات
    initializeNotifications();
});

// تهيئة التولتيبس
function initializeTooltips() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

// تهيئة المودالات
function initializeModals() {
    // إضافة تأثيرات للمودالات
    var modals = document.querySelectorAll('.modal');
    modals.forEach(function(modal) {
        modal.addEventListener('show.bs.modal', function() {
            document.body.style.overflow = 'hidden';
        });
        
        modal.addEventListener('hidden.bs.modal', function() {
            document.body.style.overflow = 'auto';
        });
    });
}

// تهيئة النماذج
function initializeForms() {
    // التحقق من صحة النماذج
    var forms = document.querySelectorAll('.needs-validation');
    Array.prototype.slice.call(forms).forEach(function(form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        }, false);
    });
    
    // تنسيق حقول الهاتف
    var phoneInputs = document.querySelectorAll('input[type="tel"]');
    phoneInputs.forEach(function(input) {
        input.addEventListener('input', function() {
            this.value = this.value.replace(/[^0-9+\-\s]/g, '');
        });
    });
    
    // تنسيق حقول الأرقام
    var numberInputs = document.querySelectorAll('input[type="number"]');
    numberInputs.forEach(function(input) {
        input.addEventListener('input', function() {
            if (this.value < 0) this.value = 0;
        });
    });
}

// تهيئة الجداول
function initializeTables() {
    // إضافة وظائف البحث للجداول
    var searchInputs = document.querySelectorAll('.table-search');
    searchInputs.forEach(function(input) {
        input.addEventListener('keyup', function() {
            var table = document.querySelector(this.dataset.table);
            var rows = table.querySelectorAll('tbody tr');
            var searchTerm = this.value.toLowerCase();
            
            rows.forEach(function(row) {
                var text = row.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        });
    });
    
    // ترقيم الصفوف
    var tables = document.querySelectorAll('.table-numbered');
    tables.forEach(function(table) {
        var rows = table.querySelectorAll('tbody tr');
        rows.forEach(function(row, index) {
            var numberCell = row.querySelector('.row-number');
            if (numberCell) {
                numberCell.textContent = index + 1;
            }
        });
    });
}

// تهيئة التقويم
function initializeCalendar() {
    var calendarEl = document.getElementById('calendar');
    if (calendarEl) {
        // يمكن إضافة مكتبة تقويم مثل FullCalendar هنا
        console.log('Calendar initialized');
    }
}

// تهيئة الإشعارات
function initializeNotifications() {
    // التحقق من دعم الإشعارات
    if ('Notification' in window) {
        if (Notification.permission === 'default') {
            Notification.requestPermission();
        }
    }
}

// عرض رسالة تأكيد
function showConfirmDialog(message, callback) {
    if (confirm(message)) {
        callback();
    }
}

// عرض رسالة نجاح
function showSuccessMessage(message) {
    showAlert(message, 'success');
}

// عرض رسالة خطأ
function showErrorMessage(message) {
    showAlert(message, 'danger');
}

// عرض رسالة تحذير
function showWarningMessage(message) {
    showAlert(message, 'warning');
}

// عرض رسالة معلومات
function showInfoMessage(message) {
    showAlert(message, 'info');
}

// عرض تنبيه
function showAlert(message, type) {
    var alertContainer = document.getElementById('alert-container');
    if (!alertContainer) {
        alertContainer = document.createElement('div');
        alertContainer.id = 'alert-container';
        alertContainer.style.position = 'fixed';
        alertContainer.style.top = '20px';
        alertContainer.style.right = '20px';
        alertContainer.style.zIndex = '9999';
        document.body.appendChild(alertContainer);
    }
    
    var alert = document.createElement('div');
    alert.className = 'alert alert-' + type + ' alert-dismissible fade show';
    alert.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    alertContainer.appendChild(alert);
    
    // إزالة التنبيه تلقائياً بعد 5 ثوان
    setTimeout(function() {
        if (alert.parentNode) {
            alert.remove();
        }
    }, 5000);
}

// تحميل البيانات بـ AJAX
function loadData(url, callback) {
    fetch(url)
        .then(response => response.json())
        .then(data => callback(data))
        .catch(error => {
            console.error('Error:', error);
            showErrorMessage('حدث خطأ في تحميل البيانات');
        });
}

// إرسال البيانات بـ AJAX
function sendData(url, data, callback) {
    fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => callback(data))
    .catch(error => {
        console.error('Error:', error);
        showErrorMessage('حدث خطأ في إرسال البيانات');
    });
}

// تنسيق التاريخ
function formatDate(date) {
    var d = new Date(date);
    var day = String(d.getDate()).padStart(2, '0');
    var month = String(d.getMonth() + 1).padStart(2, '0');
    var year = d.getFullYear();
    return day + '/' + month + '/' + year;
}

// تنسيق الوقت
function formatTime(time) {
    var t = new Date('1970-01-01T' + time + 'Z');
    return t.toLocaleTimeString('ar-EG', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
    });
}

// تنسيق العملة
function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-EG', {
        style: 'currency',
        currency: 'EGP'
    }).format(amount);
}

// طباعة عنصر معين
function printElement(elementId) {
    var element = document.getElementById(elementId);
    if (element) {
        var printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <html>
                <head>
                    <title>طباعة</title>
                    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
                    <link href="../assets/css/style.css" rel="stylesheet">
                    <style>
                        body { font-family: 'Cairo', sans-serif; }
                        @media print {
                            .no-print { display: none !important; }
                        }
                    </style>
                </head>
                <body onload="window.print(); window.close();">
                    ${element.innerHTML}
                </body>
            </html>
        `);
        printWindow.document.close();
    }
}

// تصدير الجدول إلى CSV
function exportTableToCSV(tableId, filename) {
    var table = document.getElementById(tableId);
    if (!table) return;
    
    var csv = [];
    var rows = table.querySelectorAll('tr');
    
    for (var i = 0; i < rows.length; i++) {
        var row = [];
        var cols = rows[i].querySelectorAll('td, th');
        
        for (var j = 0; j < cols.length; j++) {
            row.push(cols[j].innerText);
        }
        
        csv.push(row.join(','));
    }
    
    var csvFile = new Blob([csv.join('\n')], { type: 'text/csv' });
    var downloadLink = document.createElement('a');
    downloadLink.download = filename || 'data.csv';
    downloadLink.href = window.URL.createObjectURL(csvFile);
    downloadLink.style.display = 'none';
    document.body.appendChild(downloadLink);
    downloadLink.click();
    document.body.removeChild(downloadLink);
}

// التحقق من الاتصال بالإنترنت
function checkConnection() {
    if (!navigator.onLine) {
        showWarningMessage('لا يوجد اتصال بالإنترنت');
        return false;
    }
    return true;
}

// حفظ البيانات محلياً
function saveToLocalStorage(key, data) {
    try {
        localStorage.setItem(key, JSON.stringify(data));
        return true;
    } catch (error) {
        console.error('Error saving to localStorage:', error);
        return false;
    }
}

// استرجاع البيانات المحفوظة محلياً
function getFromLocalStorage(key) {
    try {
        var data = localStorage.getItem(key);
        return data ? JSON.parse(data) : null;
    } catch (error) {
        console.error('Error getting from localStorage:', error);
        return null;
    }
}

// تشغيل إشعار سطح المكتب
function showDesktopNotification(title, message, icon) {
    if ('Notification' in window && Notification.permission === 'granted') {
        new Notification(title, {
            body: message,
            icon: icon || '../assets/images/logo.png'
        });
    }
}

// تحديث الوقت الحالي
function updateCurrentTime() {
    var timeElements = document.querySelectorAll('.current-time');
    timeElements.forEach(function(element) {
        var now = new Date();
        element.textContent = now.toLocaleTimeString('ar-EG');
    });
}

// تشغيل تحديث الوقت كل ثانية
setInterval(updateCurrentTime, 1000);

// إضافة مستمعات الأحداث للأزرار الشائعة
document.addEventListener('click', function(e) {
    // زر الحذف
    if (e.target.classList.contains('btn-delete')) {
        e.preventDefault();
        showConfirmDialog('هل أنت متأكد من الحذف؟', function() {
            window.location.href = e.target.href;
        });
    }
    
    // زر الطباعة
    if (e.target.classList.contains('btn-print')) {
        e.preventDefault();
        var elementId = e.target.dataset.print;
        if (elementId) {
            printElement(elementId);
        } else {
            window.print();
        }
    }
    
    // زر التصدير
    if (e.target.classList.contains('btn-export')) {
        e.preventDefault();
        var tableId = e.target.dataset.table;
        var filename = e.target.dataset.filename;
        if (tableId) {
            exportTableToCSV(tableId, filename);
        }
    }
});

// معالجة الأخطاء العامة
window.addEventListener('error', function(e) {
    console.error('JavaScript Error:', e.error);
    // يمكن إرسال الأخطاء إلى الخادم للمراقبة
});

// التحقق من حالة الاتصال
window.addEventListener('online', function() {
    showSuccessMessage('تم استعادة الاتصال بالإنترنت');
});

window.addEventListener('offline', function() {
    showWarningMessage('انقطع الاتصال بالإنترنت');
});
