<?php
// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_NAME', 'collectandwin2_vitalsmm');
define('DB_USER', 'collectandwin2_vitalsmm');
define('DB_PASS', 'collectandwin2_vitalsmm');
define('DB_CHARSET', 'utf8mb4');

// إنشاء الاتصال بقاعدة البيانات
try {
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET,
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
        ]
    );
} catch (PDOException $e) {
    die("خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage());
}

// إنشاء الجداول إذا لم تكن موجودة
function createTables($pdo) {
    // جدول المستخدمين (الإدارة العامة)
    $pdo->exec("CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL,
        email VARCHAR(100) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        user_type ENUM('admin', 'super_admin') DEFAULT 'admin',
        full_name VARCHAR(100) NOT NULL,
        phone VARCHAR(20),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        is_active BOOLEAN DEFAULT TRUE
    )");

    // جدول العيادات
    $pdo->exec("CREATE TABLE IF NOT EXISTS clinics (
        id INT AUTO_INCREMENT PRIMARY KEY,
        clinic_name VARCHAR(100) NOT NULL,
        clinic_name_en VARCHAR(100),
        owner_name VARCHAR(100) NOT NULL,
        email VARCHAR(100) UNIQUE NOT NULL,
        phone VARCHAR(20) NOT NULL,
        address TEXT,
        city VARCHAR(50),
        country VARCHAR(50) DEFAULT 'مصر',
        specialization VARCHAR(100),
        license_number VARCHAR(50),
        subscription_plan ENUM('basic', 'pro', 'enterprise') DEFAULT 'basic',
        subscription_start DATE,
        subscription_end DATE,
        is_active BOOLEAN DEFAULT TRUE,
        trial_used BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )");

    // جدول مستخدمي العيادات
    $pdo->exec("CREATE TABLE IF NOT EXISTS clinic_users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        clinic_id INT NOT NULL,
        username VARCHAR(50) NOT NULL,
        email VARCHAR(100) NOT NULL,
        password VARCHAR(255) NOT NULL,
        full_name VARCHAR(100) NOT NULL,
        phone VARCHAR(20),
        role ENUM('owner', 'doctor', 'secretary', 'nurse') DEFAULT 'doctor',
        specialization VARCHAR(100),
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (clinic_id) REFERENCES clinics(id) ON DELETE CASCADE,
        UNIQUE KEY unique_clinic_username (clinic_id, username),
        UNIQUE KEY unique_clinic_email (clinic_id, email)
    )");

    // جدول المرضى
    $pdo->exec("CREATE TABLE IF NOT EXISTS patients (
        id INT AUTO_INCREMENT PRIMARY KEY,
        clinic_id INT NOT NULL,
        patient_number VARCHAR(20) UNIQUE NOT NULL,
        full_name VARCHAR(100) NOT NULL,
        phone VARCHAR(20),
        email VARCHAR(100),
        date_of_birth DATE,
        gender ENUM('male', 'female') NOT NULL,
        address TEXT,
        emergency_contact VARCHAR(100),
        emergency_phone VARCHAR(20),
        blood_type VARCHAR(5),
        allergies TEXT,
        chronic_diseases TEXT,
        notes TEXT,
        created_by INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (clinic_id) REFERENCES clinics(id) ON DELETE CASCADE,
        FOREIGN KEY (created_by) REFERENCES clinic_users(id)
    )");

    // جدول المواعيد
    $pdo->exec("CREATE TABLE IF NOT EXISTS appointments (
        id INT AUTO_INCREMENT PRIMARY KEY,
        clinic_id INT NOT NULL,
        patient_id INT NOT NULL,
        doctor_id INT NOT NULL,
        appointment_date DATE NOT NULL,
        appointment_time TIME NOT NULL,
        duration INT DEFAULT 30,
        status ENUM('scheduled', 'confirmed', 'completed', 'cancelled', 'no_show') DEFAULT 'scheduled',
        appointment_type VARCHAR(50),
        notes TEXT,
        reminder_sent BOOLEAN DEFAULT FALSE,
        created_by INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (clinic_id) REFERENCES clinics(id) ON DELETE CASCADE,
        FOREIGN KEY (patient_id) REFERENCES patients(id) ON DELETE CASCADE,
        FOREIGN KEY (doctor_id) REFERENCES clinic_users(id),
        FOREIGN KEY (created_by) REFERENCES clinic_users(id)
    )");

    // جدول الزيارات
    $pdo->exec("CREATE TABLE IF NOT EXISTS visits (
        id INT AUTO_INCREMENT PRIMARY KEY,
        clinic_id INT NOT NULL,
        patient_id INT NOT NULL,
        doctor_id INT NOT NULL,
        appointment_id INT,
        visit_date DATE NOT NULL,
        visit_time TIME NOT NULL,
        chief_complaint TEXT,
        diagnosis TEXT,
        treatment_plan TEXT,
        vital_signs JSON,
        examination_notes TEXT,
        follow_up_date DATE,
        visit_cost DECIMAL(10,2) DEFAULT 0,
        payment_status ENUM('pending', 'paid', 'partial') DEFAULT 'pending',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (clinic_id) REFERENCES clinics(id) ON DELETE CASCADE,
        FOREIGN KEY (patient_id) REFERENCES patients(id) ON DELETE CASCADE,
        FOREIGN KEY (doctor_id) REFERENCES clinic_users(id),
        FOREIGN KEY (appointment_id) REFERENCES appointments(id)
    )");

    // جدول الوصفات الطبية
    $pdo->exec("CREATE TABLE IF NOT EXISTS prescriptions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        clinic_id INT NOT NULL,
        visit_id INT NOT NULL,
        patient_id INT NOT NULL,
        doctor_id INT NOT NULL,
        prescription_number VARCHAR(20) UNIQUE NOT NULL,
        medications JSON NOT NULL,
        instructions TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (clinic_id) REFERENCES clinics(id) ON DELETE CASCADE,
        FOREIGN KEY (visit_id) REFERENCES visits(id) ON DELETE CASCADE,
        FOREIGN KEY (patient_id) REFERENCES patients(id) ON DELETE CASCADE,
        FOREIGN KEY (doctor_id) REFERENCES clinic_users(id)
    )");

    // جدول الفواتير
    $pdo->exec("CREATE TABLE IF NOT EXISTS invoices (
        id INT AUTO_INCREMENT PRIMARY KEY,
        clinic_id INT NOT NULL,
        patient_id INT NOT NULL,
        visit_id INT,
        invoice_number VARCHAR(20) UNIQUE NOT NULL,
        invoice_date DATE NOT NULL,
        due_date DATE,
        total_amount DECIMAL(10,2) NOT NULL,
        paid_amount DECIMAL(10,2) DEFAULT 0,
        payment_status ENUM('pending', 'paid', 'partial', 'overdue') DEFAULT 'pending',
        payment_method VARCHAR(50),
        notes TEXT,
        created_by INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (clinic_id) REFERENCES clinics(id) ON DELETE CASCADE,
        FOREIGN KEY (patient_id) REFERENCES patients(id) ON DELETE CASCADE,
        FOREIGN KEY (visit_id) REFERENCES visits(id),
        FOREIGN KEY (created_by) REFERENCES clinic_users(id)
    )");

    // جدول عناصر الفاتورة
    $pdo->exec("CREATE TABLE IF NOT EXISTS invoice_items (
        id INT AUTO_INCREMENT PRIMARY KEY,
        invoice_id INT NOT NULL,
        item_name VARCHAR(100) NOT NULL,
        quantity INT DEFAULT 1,
        unit_price DECIMAL(10,2) NOT NULL,
        total_price DECIMAL(10,2) NOT NULL,
        FOREIGN KEY (invoice_id) REFERENCES invoices(id) ON DELETE CASCADE
    )");

    // جدول ملفات المرضى
    $pdo->exec("CREATE TABLE IF NOT EXISTS patient_files (
        id INT AUTO_INCREMENT PRIMARY KEY,
        clinic_id INT NOT NULL,
        patient_id INT NOT NULL,
        visit_id INT,
        file_name VARCHAR(255) NOT NULL,
        file_path VARCHAR(500) NOT NULL,
        file_type VARCHAR(50),
        file_size INT,
        description TEXT,
        uploaded_by INT,
        uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (clinic_id) REFERENCES clinics(id) ON DELETE CASCADE,
        FOREIGN KEY (patient_id) REFERENCES patients(id) ON DELETE CASCADE,
        FOREIGN KEY (visit_id) REFERENCES visits(id),
        FOREIGN KEY (uploaded_by) REFERENCES clinic_users(id)
    )");

    // جدول الاشتراكات والدفعات
    $pdo->exec("CREATE TABLE IF NOT EXISTS subscription_payments (
        id INT AUTO_INCREMENT PRIMARY KEY,
        clinic_id INT NOT NULL,
        plan_type ENUM('basic', 'pro', 'enterprise') NOT NULL,
        amount DECIMAL(10,2) NOT NULL,
        payment_method VARCHAR(50),
        payment_status ENUM('pending', 'completed', 'failed') DEFAULT 'pending',
        payment_date DATE,
        start_date DATE NOT NULL,
        end_date DATE NOT NULL,
        transaction_id VARCHAR(100),
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (clinic_id) REFERENCES clinics(id) ON DELETE CASCADE
    )");

    // جدول الأدوية المحفوظة
    $pdo->exec("CREATE TABLE IF NOT EXISTS medications (
        id INT AUTO_INCREMENT PRIMARY KEY,
        clinic_id INT,
        medication_name VARCHAR(100) NOT NULL,
        medication_name_en VARCHAR(100),
        dosage VARCHAR(50),
        form VARCHAR(50),
        instructions TEXT,
        is_global BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (clinic_id) REFERENCES clinics(id) ON DELETE CASCADE
    )");

    // إدراج بيانات تجريبية للإدارة العامة
    $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
    $pdo->exec("INSERT IGNORE INTO users (username, email, password, user_type, full_name, phone) 
                VALUES ('admin', '<EMAIL>', '$admin_password', 'super_admin', 'مدير النظام', '01000000000')");
}

// تشغيل إنشاء الجداول
createTables($pdo);
?>
