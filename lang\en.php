<?php
// English Language File
$text = [
    // Main Titles
    'site_title' => 'Hakim',
    'tagline' => 'Smart Clinic Management System',
    
    // Main Menu
    'features' => 'Features',
    'pricing' => 'Pricing',
    'contact' => 'Contact',
    'login' => 'Login',
    'register' => 'Register',
    'logout' => 'Logout',
    
    // Homepage
    'hero_title' => 'Manage Your Clinic Smartly with Hakim',
    'hero_subtitle' => 'Comprehensive clinic management system that helps you organize patients, appointments, prescriptions, and invoices with ease',
    'start_free_trial' => 'Start Free Trial',
    'learn_more' => 'Learn More',
    
    // Features
    'features_title' => 'Hakim System Features',
    'features_subtitle' => 'Everything you need to manage your clinic efficiently',
    
    'patient_management' => 'Patient Management',
    'patient_management_desc' => 'Comprehensive system for managing patient data, medical history, and medical files',
    
    'appointment_system' => 'Appointment System',
    'appointment_system_desc' => 'Easy appointment scheduling with automatic notifications for patients and doctors',
    
    'prescriptions' => 'Electronic Prescriptions',
    'prescriptions_desc' => 'Professional prescription writing and printing with medication database',
    
    'billing_system' => 'Billing System',
    'billing_system_desc' => 'Create and manage invoices with payment tracking and financial reports',
    
    'reports' => 'Reports & Analytics',
    'reports_desc' => 'Comprehensive reports on clinic performance, patients, and revenue',
    
    'mobile_responsive' => 'Mobile Responsive',
    'mobile_responsive_desc' => 'Works efficiently on all devices and screens',
    
    // Subscription Plans
    'pricing_title' => 'Subscription Plans',
    'pricing_subtitle' => 'Choose the right plan for your clinic',
    
    'basic_plan' => 'Basic Plan',
    'pro_plan' => 'Pro Plan',
    'enterprise_plan' => 'Enterprise Plan',
    
    'monthly' => 'Monthly',
    'yearly' => 'Yearly',
    'choose_plan' => 'Choose This Plan',
    
    // Login Forms
    'login_title' => 'Login',
    'username_email' => 'Username or Email',
    'password' => 'Password',
    'remember_me' => 'Remember Me',
    'forgot_password' => 'Forgot Password?',
    'login_button' => 'Login',
    'no_account' => 'Don\'t have an account?',
    'create_account' => 'Create New Account',
    
    // Registration Forms
    'register_title' => 'Create New Account',
    'clinic_name' => 'Clinic Name',
    'owner_name' => 'Owner Name',
    'email' => 'Email',
    'phone' => 'Phone',
    'address' => 'Address',
    'city' => 'City',
    'specialization' => 'Specialization',
    'confirm_password' => 'Confirm Password',
    'agree_terms' => 'I agree to Terms and Conditions',
    'register_button' => 'Create Account',
    'have_account' => 'Already have an account?',
    
    // Dashboard
    'dashboard' => 'Dashboard',
    'welcome_back' => 'Welcome Back',
    'today_appointments' => 'Today\'s Appointments',
    'total_patients' => 'Total Patients',
    'monthly_revenue' => 'Monthly Revenue',
    'pending_payments' => 'Pending Payments',
    
    // Menus
    'patients' => 'Patients',
    'appointments' => 'Appointments',
    'visits' => 'Visits',
    'invoices' => 'Invoices',
    'reports' => 'Reports',
    'settings' => 'Settings',
    'profile' => 'Profile',
    
    // Patients
    'add_patient' => 'Add New Patient',
    'patient_list' => 'Patient List',
    'patient_details' => 'Patient Details',
    'patient_history' => 'Medical History',
    'full_name' => 'Full Name',
    'date_of_birth' => 'Date of Birth',
    'gender' => 'Gender',
    'male' => 'Male',
    'female' => 'Female',
    'blood_type' => 'Blood Type',
    'allergies' => 'Allergies',
    'chronic_diseases' => 'Chronic Diseases',
    'emergency_contact' => 'Emergency Contact',
    'emergency_phone' => 'Emergency Phone',
    'notes' => 'Notes',
    
    // Appointments
    'add_appointment' => 'Add New Appointment',
    'appointment_date' => 'Appointment Date',
    'appointment_time' => 'Appointment Time',
    'doctor' => 'Doctor',
    'patient' => 'Patient',
    'duration' => 'Duration',
    'status' => 'Status',
    'scheduled' => 'Scheduled',
    'confirmed' => 'Confirmed',
    'completed' => 'Completed',
    'cancelled' => 'Cancelled',
    'no_show' => 'No Show',
    
    // Buttons and Actions
    'save' => 'Save',
    'cancel' => 'Cancel',
    'edit' => 'Edit',
    'delete' => 'Delete',
    'view' => 'View',
    'print' => 'Print',
    'search' => 'Search',
    'filter' => 'Filter',
    'export' => 'Export',
    'import' => 'Import',
    'add' => 'Add',
    'update' => 'Update',
    'submit' => 'Submit',
    'back' => 'Back',
    'next' => 'Next',
    'previous' => 'Previous',
    
    // Messages
    'success' => 'Success',
    'error' => 'Error',
    'warning' => 'Warning',
    'info' => 'Information',
    'confirm_delete' => 'Are you sure you want to delete?',
    'no_data' => 'No data available',
    'loading' => 'Loading...',
    'please_wait' => 'Please wait...',
    
    // Dates and Times
    'today' => 'Today',
    'yesterday' => 'Yesterday',
    'tomorrow' => 'Tomorrow',
    'this_week' => 'This Week',
    'this_month' => 'This Month',
    'this_year' => 'This Year',
    
    // Days of Week
    'sunday' => 'Sunday',
    'monday' => 'Monday',
    'tuesday' => 'Tuesday',
    'wednesday' => 'Wednesday',
    'thursday' => 'Thursday',
    'friday' => 'Friday',
    'saturday' => 'Saturday',
    
    // Months
    'january' => 'January',
    'february' => 'February',
    'march' => 'March',
    'april' => 'April',
    'may' => 'May',
    'june' => 'June',
    'july' => 'July',
    'august' => 'August',
    'september' => 'September',
    'october' => 'October',
    'november' => 'November',
    'december' => 'December',
];
?>
