# نظام حكيم - ملف .htaccess
# Hakim Clinic Management System - .htaccess Configuration

# تفعيل محرك إعادة الكتابة
RewriteEngine On

# منع الوصول للملفات الحساسة
<Files "config/database.php">
    Order Allow,Deny
    Deny from all
</Files>

<Files "includes/functions.php">
    Order Allow,Deny
    Deny from all
</Files>

<Files ".htaccess">
    Order Allow,Deny
    Deny from all
</Files>

<Files "*.sql">
    Order Allow,Deny
    Deny from all
</Files>

<Files "README.md">
    Order Allow,Deny
    Deny from all
</Files>

# منع الوصول للمجلدات الحساسة
<Directory "config">
    Order Allow,Deny
    Deny from all
</Directory>

<Directory "includes">
    Order Allow,Deny
    Deny from all
</Directory>

# حماية ملفات الرفع
<Directory "uploads">
    # السماح بالصور والملفات المسموحة فقط
    <FilesMatch "\.(jpg|jpeg|png|gif|pdf|doc|docx)$">
        Order Allow,Deny
        Allow from all
    </FilesMatch>
    
    # منع تنفيذ ملفات PHP في مجلد الرفع
    <FilesMatch "\.php$">
        Order Allow,Deny
        Deny from all
    </FilesMatch>
</Directory>

# ضغط الملفات لتحسين الأداء
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# تفعيل التخزين المؤقت
<IfModule mod_expires.c>
    ExpiresActive On
    
    # الصور
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"
    
    # CSS و JavaScript
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"
    
    # الخطوط
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresByType application/font-woff "access plus 1 year"
    ExpiresByType application/font-woff2 "access plus 1 year"
    
    # HTML
    ExpiresByType text/html "access plus 1 hour"
</IfModule>

# إعدادات الأمان
<IfModule mod_headers.c>
    # منع clickjacking
    Header always append X-Frame-Options SAMEORIGIN
    
    # منع MIME type sniffing
    Header set X-Content-Type-Options nosniff
    
    # تفعيل XSS protection
    Header set X-XSS-Protection "1; mode=block"
    
    # إخفاء معلومات الخادم
    Header unset Server
    Header unset X-Powered-By
    
    # HTTPS Strict Transport Security
    Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains"
    
    # Content Security Policy
    Header set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://fonts.googleapis.com; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com; img-src 'self' data:; connect-src 'self';"
</IfModule>

# منع الوصول للملفات المخفية
<FilesMatch "^\.">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# منع الوصول لملفات النسخ الاحتياطية
<FilesMatch "\.(bak|backup|old|tmp|temp)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# تحديد صفحات الخطأ المخصصة
ErrorDocument 404 /404.php
ErrorDocument 403 /403.php
ErrorDocument 500 /500.php

# إعادة توجيه HTTP إلى HTTPS (اختياري)
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# إعادة توجيه www إلى non-www (اختياري)
# RewriteCond %{HTTP_HOST} ^www\.(.*)$ [NC]
# RewriteRule ^(.*)$ https://%1/$1 [R=301,L]

# منع الوصول المباشر لملفات PHP في مجلدات معينة
<FilesMatch "^(config|includes).*\.php$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# تحسين الأداء - إزالة ETags
<IfModule mod_headers.c>
    Header unset ETag
</IfModule>
FileETag None

# منع directory browsing
Options -Indexes

# تحديد الترميز الافتراضي
AddDefaultCharset UTF-8

# تحديد نوع المحتوى للملفات
<IfModule mod_mime.c>
    AddType application/javascript .js
    AddType text/css .css
    AddType image/svg+xml .svg
    AddType application/font-woff .woff
    AddType application/font-woff2 .woff2
</IfModule>

# حد أقصى لحجم الملف المرفوع (يمكن تعديله حسب الحاجة)
php_value upload_max_filesize 10M
php_value post_max_size 10M
php_value max_execution_time 300
php_value max_input_time 300

# تفعيل session cookie security
php_value session.cookie_httponly 1
php_value session.cookie_secure 1
php_value session.use_only_cookies 1

# منع عرض أخطاء PHP في الإنتاج
php_flag display_errors Off
php_flag log_errors On

# إعدادات إضافية للأمان
<IfModule mod_rewrite.c>
    # منع الوصول لملفات النظام
    RewriteRule ^(config|includes|lang)/ - [F,L]
    
    # منع الوصول للملفات الحساسة
    RewriteRule \.(sql|md|txt|log)$ - [F,L]
    
    # منع SQL injection في URL
    RewriteCond %{QUERY_STRING} (\<|%3C).*script.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} GLOBALS(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} _REQUEST(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*iframe.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*object.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*embed.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (SELECT|UNION|INSERT|DROP|DELETE|UPDATE|CREATE|ALTER|EXEC) [NC]
    RewriteRule .* - [F,L]
</IfModule>

# تحسين الذاكرة
php_value memory_limit 256M

# إعدادات الجلسة
php_value session.gc_maxlifetime 3600
php_value session.cache_expire 180

# منع hotlinking للصور (اختياري)
# RewriteCond %{HTTP_REFERER} !^$
# RewriteCond %{HTTP_REFERER} !^http(s)?://(www\.)?yourdomain.com [NC]
# RewriteRule \.(jpg|jpeg|png|gif)$ - [NC,F,L]
