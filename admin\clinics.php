<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] != 'admin' && $_SESSION['user_type'] != 'super_admin') {
    header('Location: ../public/login.php');
    exit();
}

// تحديد اللغة
$lang = isset($_SESSION['lang']) ? $_SESSION['lang'] : 'ar';
require_once "../lang/{$lang}.php";

$action = isset($_GET['action']) ? $_GET['action'] : 'list';
$clinic_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

$error_message = '';
$success_message = '';

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if ($action == 'activate' || $action == 'deactivate') {
        $is_active = $action == 'activate' ? 1 : 0;
        $status_text = $action == 'activate' ? 'تفعيل' : 'إلغاء تفعيل';

        try {
            $stmt = $pdo->prepare("UPDATE clinics SET is_active = ? WHERE id = ?");
            $stmt->execute([$is_active, $clinic_id]);

            $success_message = "تم $status_text العيادة بنجاح";
            log_activity($pdo, $_SESSION['user_id'], $action . '_clinic', "Clinic ID: $clinic_id");
        } catch (Exception $e) {
            $error_message = "حدث خطأ في $status_text العيادة";
        }
    }

    if ($action == 'extend_subscription') {
        $months = (int)$_POST['months'];
        $plan = sanitize_input($_POST['plan']);

        if ($months > 0 && in_array($plan, ['basic', 'pro', 'enterprise'])) {
            try {
                // جلب تاريخ انتهاء الاشتراك الحالي
                $stmt = $pdo->prepare("SELECT subscription_end FROM clinics WHERE id = ?");
                $stmt->execute([$clinic_id]);
                $clinic = $stmt->fetch();

                $current_end = $clinic['subscription_end'];
                $start_date = max(date('Y-m-d'), $current_end);
                $new_end_date = date('Y-m-d', strtotime($start_date . " +$months months"));

                // تحديث الاشتراك
                $stmt = $pdo->prepare("UPDATE clinics SET subscription_plan = ?, subscription_end = ? WHERE id = ?");
                $stmt->execute([$plan, $new_end_date, $clinic_id]);

                // إضافة سجل دفع
                $amount = $plan == 'basic' ? 299 : ($plan == 'pro' ? 599 : 999);
                $total_amount = $amount * $months;

                $stmt = $pdo->prepare("INSERT INTO subscription_payments (clinic_id, plan_type, amount, payment_status, payment_date, start_date, end_date, notes) VALUES (?, ?, ?, 'completed', CURDATE(), ?, ?, 'Extended by admin')");
                $stmt->execute([$clinic_id, $plan, $total_amount, $start_date, $new_end_date]);

                $success_message = "تم تمديد الاشتراك بنجاح لمدة $months شهر";
                log_activity($pdo, $_SESSION['user_id'], 'extend_subscription', "Clinic ID: $clinic_id, Months: $months, Plan: $plan");
            } catch (Exception $e) {
                $error_message = 'حدث خطأ في تمديد الاشتراك';
            }
        } else {
            $error_message = 'بيانات غير صحيحة';
        }
    }
}

// جلب قائمة العيادات
$clinics = [];
$search = isset($_GET['search']) ? sanitize_input($_GET['search']) : '';
$status_filter = isset($_GET['status']) ? $_GET['status'] : '';
$plan_filter = isset($_GET['plan']) ? $_GET['plan'] : '';
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$per_page = 20;
$offset = ($page - 1) * $per_page;

try {
    $where_conditions = [];
    $params = [];

    if (!empty($search)) {
        $where_conditions[] = "(clinic_name LIKE ? OR owner_name LIKE ? OR email LIKE ?)";
        $search_term = "%$search%";
        $params[] = $search_term;
        $params[] = $search_term;
        $params[] = $search_term;
    }

    if ($status_filter === 'active') {
        $where_conditions[] = "is_active = 1 AND subscription_end >= CURDATE()";
    } elseif ($status_filter === 'expired') {
        $where_conditions[] = "subscription_end < CURDATE()";
    } elseif ($status_filter === 'inactive') {
        $where_conditions[] = "is_active = 0";
    }

    if (!empty($plan_filter)) {
        $where_conditions[] = "subscription_plan = ?";
        $params[] = $plan_filter;
    }

    $where_clause = !empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "";

    // عدد العيادات الإجمالي
    $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM clinics $where_clause");
    $stmt->execute($params);
    $total_clinics = $stmt->fetch()['total'];
    $total_pages = ceil($total_clinics / $per_page);

    // جلب العيادات
    $stmt = $pdo->prepare("SELECT *,
        CASE
            WHEN subscription_end < CURDATE() THEN 'expired'
            WHEN subscription_end BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 7 DAY) THEN 'expiring'
            ELSE 'active'
        END as status
        FROM clinics $where_clause
        ORDER BY created_at DESC
        LIMIT $per_page OFFSET $offset");
    $stmt->execute($params);
    $clinics = $stmt->fetchAll();

} catch (Exception $e) {
    $error_message = 'حدث خطأ في جلب البيانات';
}
?>
<!DOCTYPE html>
<html lang="<?php echo $lang; ?>" dir="<?php echo $lang == 'ar' ? 'rtl' : 'ltr'; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة العيادات - <?php echo $text['site_title']; ?></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container-fluid">
        <div class="row">
            <!-- الشريط الجانبي -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h4 class="text-primary fw-bold">
                            <i class="fas fa-user-shield me-2"></i>
                            لوحة الإدارة
                        </h4>
                        <small class="text-muted">مرحباً، <?php echo $_SESSION['full_name']; ?></small>
                    </div>

                    <ul class="sidebar-nav">
                        <li><a href="dashboard.php"><i class="fas fa-tachometer-alt"></i>الرئيسية</a></li>
                        <li><a href="clinics.php" class="active"><i class="fas fa-hospital"></i>إدارة العيادات</a></li>
                        <li><a href="subscriptions.php"><i class="fas fa-credit-card"></i>الاشتراكات</a></li>
                        <li><a href="payments.php"><i class="fas fa-money-bill-wave"></i>المدفوعات</a></li>
                        <li><a href="reports.php"><i class="fas fa-chart-bar"></i>التقارير</a></li>
                        <li><a href="settings.php"><i class="fas fa-cog"></i>الإعدادات</a></li>
                        <li><a href="users.php"><i class="fas fa-users-cog"></i>المستخدمين</a></li>
                        <li><a href="../public/login.php?logout=1" class="text-danger"><i class="fas fa-sign-out-alt"></i>تسجيل الخروج</a></li>
                    </ul>
                </div>
            </nav>

            <!-- المحتوى الرئيسي -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-hospital me-2"></i>
                        إدارة العيادات
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary btn-export" data-table="clinicsTable" data-filename="clinics.csv">
                                <i class="fas fa-download me-1"></i>
                                تصدير
                            </button>
                        </div>
                    </div>
                </div>

                <!-- الرسائل -->
                <?php if ($error_message): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?php echo $error_message; ?>
                    </div>
                <?php endif; ?>

                <?php if ($success_message): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        <?php echo $success_message; ?>
                    </div>
                <?php endif; ?>

                <!-- البحث والفلترة -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-4">
                                <label for="search" class="form-label">البحث</label>
                                <input type="text" class="form-control" id="search" name="search"
                                       placeholder="البحث بالاسم أو البريد الإلكتروني"
                                       value="<?php echo htmlspecialchars($search); ?>">
                            </div>
                            <div class="col-md-3">
                                <label for="status" class="form-label">الحالة</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="">جميع الحالات</option>
                                    <option value="active" <?php echo $status_filter == 'active' ? 'selected' : ''; ?>>نشطة</option>
                                    <option value="expired" <?php echo $status_filter == 'expired' ? 'selected' : ''; ?>>منتهية الصلاحية</option>
                                    <option value="inactive" <?php echo $status_filter == 'inactive' ? 'selected' : ''; ?>>غير مفعلة</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="plan" class="form-label">خطة الاشتراك</label>
                                <select class="form-select" id="plan" name="plan">
                                    <option value="">جميع الخطط</option>
                                    <option value="basic" <?php echo $plan_filter == 'basic' ? 'selected' : ''; ?>>أساسي</option>
                                    <option value="pro" <?php echo $plan_filter == 'pro' ? 'selected' : ''; ?>>متقدم</option>
                                    <option value="enterprise" <?php echo $plan_filter == 'enterprise' ? 'selected' : ''; ?>>مؤسسات</option>
                                </select>
                            </div>
                            <div class="col-md-2 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="fas fa-search me-1"></i>
                                    بحث
                                </button>
                                <a href="clinics.php" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-1"></i>
                                    مسح
                                </a>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- جدول العيادات -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            قائمة العيادات (<?php echo number_format($total_clinics); ?> عيادة)
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($clinics)): ?>
                            <div class="table-responsive">
                                <table class="table table-hover" id="clinicsTable">
                                    <thead>
                                        <tr>
                                            <th>اسم العيادة</th>
                                            <th>المالك</th>
                                            <th>البريد الإلكتروني</th>
                                            <th>الهاتف</th>
                                            <th>خطة الاشتراك</th>
                                            <th>تاريخ الانتهاء</th>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($clinics as $clinic): ?>
                                            <tr>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($clinic['clinic_name']); ?></strong>
                                                    <?php if ($clinic['specialization']): ?>
                                                        <br><small class="text-muted"><?php echo htmlspecialchars($clinic['specialization']); ?></small>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo htmlspecialchars($clinic['owner_name']); ?></td>
                                                <td><?php echo htmlspecialchars($clinic['email']); ?></td>
                                                <td><?php echo htmlspecialchars($clinic['phone']); ?></td>
                                                <td>
                                                    <span class="badge bg-<?php echo $clinic['subscription_plan'] == 'basic' ? 'info' : ($clinic['subscription_plan'] == 'pro' ? 'warning' : 'success'); ?>">
                                                        <?php echo $clinic['subscription_plan']; ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <?php echo format_date_arabic($clinic['subscription_end']); ?>
                                                    <?php
                                                    $days_left = (strtotime($clinic['subscription_end']) - strtotime(date('Y-m-d'))) / (60 * 60 * 24);
                                                    if ($days_left < 0) {
                                                        echo '<br><small class="text-danger">منتهية منذ ' . abs(ceil($days_left)) . ' يوم</small>';
                                                    } elseif ($days_left <= 7) {
                                                        echo '<br><small class="text-warning">تنتهي خلال ' . ceil($days_left) . ' يوم</small>';
                                                    }
                                                    ?>
                                                </td>
                                                <td>
                                                    <?php if (!$clinic['is_active']): ?>
                                                        <span class="badge bg-secondary">غير مفعلة</span>
                                                    <?php elseif ($clinic['status'] == 'expired'): ?>
                                                        <span class="badge bg-danger">منتهية</span>
                                                    <?php elseif ($clinic['status'] == 'expiring'): ?>
                                                        <span class="badge bg-warning">تنتهي قريباً</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-success">نشطة</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <a href="clinic_details.php?id=<?php echo $clinic['id']; ?>" class="btn btn-outline-info" title="عرض التفاصيل">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        <button type="button" class="btn btn-outline-success" title="تمديد الاشتراك" data-bs-toggle="modal" data-bs-target="#extendModal" data-clinic-id="<?php echo $clinic['id']; ?>" data-clinic-name="<?php echo htmlspecialchars($clinic['clinic_name']); ?>">
                                                            <i class="fas fa-plus"></i>
                                                        </button>
                                                        <?php if ($clinic['is_active']): ?>
                                                            <form method="POST" style="display: inline;">
                                                                <input type="hidden" name="action" value="deactivate">
                                                                <button type="submit" class="btn btn-outline-warning" title="إلغاء التفعيل" onclick="return confirm('هل أنت متأكد من إلغاء تفعيل هذه العيادة؟')">
                                                                    <i class="fas fa-pause"></i>
                                                                </button>
                                                            </form>
                                                        <?php else: ?>
                                                            <form method="POST" style="display: inline;">
                                                                <input type="hidden" name="action" value="activate">
                                                                <button type="submit" class="btn btn-outline-success" title="تفعيل">
                                                                    <i class="fas fa-play"></i>
                                                                </button>
                                                            </form>
                                                        <?php endif; ?>
                                                        <a href="mailto:<?php echo $clinic['email']; ?>" class="btn btn-outline-primary" title="إرسال بريد">
                                                            <i class="fas fa-envelope"></i>
                                                        </a>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>

                            <!-- الترقيم -->
                            <?php if ($total_pages > 1): ?>
                                <nav aria-label="Clinics pagination">
                                    <ul class="pagination justify-content-center">
                                        <?php if ($page > 1): ?>
                                            <li class="page-item">
                                                <a class="page-link" href="?page=<?php echo $page - 1; ?>&search=<?php echo urlencode($search); ?>&status=<?php echo urlencode($status_filter); ?>&plan=<?php echo urlencode($plan_filter); ?>">السابق</a>
                                            </li>
                                        <?php endif; ?>

                                        <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                                            <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                                <a class="page-link" href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&status=<?php echo urlencode($status_filter); ?>&plan=<?php echo urlencode($plan_filter); ?>"><?php echo $i; ?></a>
                                            </li>
                                        <?php endfor; ?>

                                        <?php if ($page < $total_pages): ?>
                                            <li class="page-item">
                                                <a class="page-link" href="?page=<?php echo $page + 1; ?>&search=<?php echo urlencode($search); ?>&status=<?php echo urlencode($status_filter); ?>&plan=<?php echo urlencode($plan_filter); ?>">التالي</a>
                                            </li>
                                        <?php endif; ?>
                                    </ul>
                                </nav>
                            <?php endif; ?>
                        <?php else: ?>
                            <div class="text-center py-5">
                                <i class="fas fa-hospital fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">لا توجد عيادات مسجلة</h5>
                                <p class="text-muted">لم يتم العثور على عيادات تطابق معايير البحث</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- مودال تمديد الاشتراك -->
    <div class="modal fade" id="extendModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تمديد الاشتراك</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="extend_subscription">
                        <input type="hidden" id="extend_clinic_id" name="clinic_id">

                        <div class="mb-3">
                            <label class="form-label">اسم العيادة</label>
                            <input type="text" class="form-control" id="extend_clinic_name" readonly>
                        </div>

                        <div class="mb-3">
                            <label for="extend_plan" class="form-label">خطة الاشتراك</label>
                            <select class="form-select" id="extend_plan" name="plan" required>
                                <option value="basic">أساسي - 299 ج.م/شهر</option>
                                <option value="pro">متقدم - 599 ج.م/شهر</option>
                                <option value="enterprise">مؤسسات - 999 ج.م/شهر</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="extend_months" class="form-label">عدد الشهور</label>
                            <select class="form-select" id="extend_months" name="months" required>
                                <option value="1">شهر واحد</option>
                                <option value="3">3 شهور</option>
                                <option value="6">6 شهور</option>
                                <option value="12">12 شهر</option>
                            </select>
                        </div>

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            سيتم تمديد الاشتراك من تاريخ انتهاء الاشتراك الحالي أو من اليوم أيهما أبعد.
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">تمديد الاشتراك</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="../assets/js/main.js"></script>

    <script>
        // تمديد الاشتراك
        document.addEventListener('DOMContentLoaded', function() {
            const extendModal = document.getElementById('extendModal');
            extendModal.addEventListener('show.bs.modal', function(event) {
                const button = event.relatedTarget;
                const clinicId = button.getAttribute('data-clinic-id');
                const clinicName = button.getAttribute('data-clinic-name');

                document.getElementById('extend_clinic_id').value = clinicId;
                document.getElementById('extend_clinic_name').value = clinicName;
            });
        });
    </script>
</body>
</html>