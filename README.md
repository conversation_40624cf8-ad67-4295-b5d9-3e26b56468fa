# نظام حكيم - منصة SaaS لإدارة العيادات الطبية

## نظرة عامة

حكيم هو نظام شامل لإدارة العيادات الطبية مصمم كمنصة SaaS (Software as a Service) يساعد الأطباء وأصحاب العيادات على إدارة عياداتهم بكفاءة عالية. النظام مطور باستخدام PHP و MySQL ويدعم اللغتين العربية والإنجليزية.

## المميزات الرئيسية

### 🏥 إدارة العيادات
- تسجيل العيادات الجديدة مع تجربة مجانية 7 أيام
- إدارة بيانات العيادة والفروع المتعددة
- نظام صلاحيات متقدم للطاقم الطبي

### 👥 إدارة المرضى
- ملفات شاملة للمرضى مع التاريخ المرضي
- نظام ترقيم فريد لكل مريض
- إدارة بيانات الاتصال والطوارئ
- تتبع الحساسية والأمراض المزمنة

### 📅 نظام المواعيد الذكي
- جدولة المواعيد بسهولة
- تقويم تفاعلي لعرض المواعيد
- إشعارات تلقائية للمرضى والأطباء
- إدارة حالات المواعيد (مجدول، مؤكد، مكتمل، ملغي)

### 💊 الوصفات الإلكترونية
- كتابة وطباعة الوصفات الطبية
- قاعدة بيانات شاملة للأدوية
- حفظ الوصفات السابقة للمراجعة
- تخصيص تعليمات الاستخدام

### 💳 نظام الفواتير والمحاسبة
- إنشاء فواتير احترافية
- تتبع المدفوعات والمستحقات
- تقارير مالية شاملة
- دعم طرق دفع متعددة

### 📊 التقارير والإحصائيات
- تقارير شاملة عن أداء العيادة
- إحصائيات المرضى والزيارات
- تقارير الإيرادات والمصروفات
- تصدير التقارير بصيغ مختلفة

### 🔐 الأمان والخصوصية
- تشفير كلمات المرور
- نظام صلاحيات متقدم
- حماية من CSRF
- تسجيل جميع الأنشطة

## التقنيات المستخدمة

- **Backend**: PHP 7.4+
- **Database**: MySQL 5.7+
- **Frontend**: HTML5, CSS3, JavaScript
- **Framework CSS**: Bootstrap 5.3
- **Icons**: Font Awesome 6.0
- **Charts**: Chart.js
- **Fonts**: Google Fonts (Cairo)

## متطلبات النظام

- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- Apache/Nginx Web Server
- مساحة تخزين: 500MB على الأقل
- ذاكرة: 512MB RAM على الأقل

## التثبيت والإعداد

### 1. تحميل الملفات
```bash
git clone https://github.com/your-repo/hakim-clinic-system.git
cd hakim-clinic-system
```

### 2. إعداد قاعدة البيانات
1. أنشئ قاعدة بيانات جديدة في MySQL
2. قم بتحديث بيانات الاتصال في `config/database.php`:
```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'your_database_name');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
```

### 3. رفع الملفات
ارفع جميع الملفات إلى مجلد الويب الخاص بك

### 4. تشغيل النظام
1. افتح المتصفح وانتقل إلى رابط موقعك
2. سيتم إنشاء الجداول تلقائياً عند أول زيارة
3. استخدم بيانات الإدارة الافتراضية:
   - اسم المستخدم: `admin`
   - كلمة المرور: `admin123`

## هيكل المشروع

```
hakim/
├── admin/                  # لوحة تحكم الإدارة العامة
│   ├── dashboard.php
│   ├── clinics.php
│   └── ...
├── clinic/                 # لوحة تحكم العيادات
│   ├── dashboard.php
│   ├── patients.php
│   └── ...
├── public/                 # الصفحات العامة
│   ├── login.php
│   ├── register.php
│   └── ...
├── config/                 # ملفات الإعدادات
│   └── database.php
├── includes/               # الملفات المشتركة
│   └── functions.php
├── lang/                   # ملفات اللغة
│   ├── ar.php
│   └── en.php
├── assets/                 # الملفات الثابتة
│   ├── css/
│   ├── js/
│   └── images/
├── uploads/                # ملفات المرضى المرفوعة
└── index.php              # الصفحة الرئيسية
```

## خطط الاشتراك

### الخطة الأساسية - 299 ج.م/شهر
- حتى 500 مريض
- مواعيد غير محدودة
- الوصفات الإلكترونية
- الفواتير والمحاسبة
- التقارير الأساسية
- دعم فني

### الخطة المتقدمة - 599 ج.م/شهر
- مرضى غير محدود
- جميع مميزات الخطة الأساسية
- تقارير متقدمة
- إشعارات SMS
- نسخ احتياطية
- دعم فني أولوية

### خطة المؤسسات - 999 ج.م/شهر
- جميع المميزات
- عدة فروع
- طاقم طبي غير محدود
- API متقدم
- تخصيص كامل
- دعم فني 24/7

## الاستخدام

### للعيادات الجديدة
1. انتقل إلى الصفحة الرئيسية
2. اضغط على "إنشاء حساب جديد"
3. املأ بيانات العيادة
4. استمتع بالتجربة المجانية لمدة 7 أيام

### للإدارة العامة
1. انتقل إلى صفحة تسجيل الدخول
2. استخدم بيانات الإدارة
3. أدر العيادات والاشتراكات

## الدعم الفني

- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +20 100 000 0000
- **ساعات العمل**: 9 صباحاً - 6 مساءً (الأحد - الخميس)

## المساهمة في المشروع

نرحب بمساهماتكم في تطوير النظام:

1. Fork المشروع
2. أنشئ branch جديد للميزة (`git checkout -b feature/AmazingFeature`)
3. Commit التغييرات (`git commit -m 'Add some AmazingFeature'`)
4. Push إلى Branch (`git push origin feature/AmazingFeature`)
5. افتح Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## الأمان

إذا اكتشفت ثغرة أمنية، يرجى إرسال بريد إلكتروني إلى <EMAIL> بدلاً من استخدام نظام التتبع العام.

## التحديثات المستقبلية

- [ ] تطبيق موبايل
- [ ] تكامل مع أنظمة الدفع الإلكتروني
- [ ] API للتكامل مع الأنظمة الخارجية
- [ ] نظام التقييمات والمراجعات
- [ ] تقارير ذكية باستخدام الذكاء الاصطناعي

## الشكر والتقدير

شكر خاص لجميع المساهمين في تطوير هذا النظام والمكتبات مفتوحة المصدر المستخدمة.

---

**حكيم** - نظام إدارة العيادات الذكي 🏥

للمزيد من المعلومات، زوروا موقعنا: [hakim.com](https://hakim.com)
