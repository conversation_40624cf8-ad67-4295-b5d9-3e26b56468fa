<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] != 'admin' && $_SESSION['user_type'] != 'super_admin') {
    header('Location: ../public/login.php');
    exit();
}

// تحديد اللغة
$lang = isset($_SESSION['lang']) ? $_SESSION['lang'] : 'ar';
require_once "../lang/{$lang}.php";

// جلب الإحصائيات
try {
    // إجمالي العيادات
    $stmt = $pdo->query("SELECT COUNT(*) as total_clinics FROM clinics WHERE is_active = 1");
    $total_clinics = $stmt->fetch()['total_clinics'];
    
    // العيادات النشطة (لديها اشتراك ساري)
    $stmt = $pdo->query("SELECT COUNT(*) as active_clinics FROM clinics WHERE is_active = 1 AND subscription_end >= CURDATE()");
    $active_clinics = $stmt->fetch()['active_clinics'];
    
    // العيادات المنتهية الصلاحية
    $stmt = $pdo->query("SELECT COUNT(*) as expired_clinics FROM clinics WHERE is_active = 1 AND subscription_end < CURDATE()");
    $expired_clinics = $stmt->fetch()['expired_clinics'];
    
    // إجمالي المرضى
    $stmt = $pdo->query("SELECT COUNT(*) as total_patients FROM patients");
    $total_patients = $stmt->fetch()['total_patients'];
    
    // المواعيد اليوم
    $stmt = $pdo->query("SELECT COUNT(*) as today_appointments FROM appointments WHERE appointment_date = CURDATE()");
    $today_appointments = $stmt->fetch()['today_appointments'];
    
    // الإيرادات الشهرية
    $stmt = $pdo->query("SELECT SUM(amount) as monthly_revenue FROM subscription_payments WHERE payment_status = 'completed' AND MONTH(payment_date) = MONTH(CURDATE()) AND YEAR(payment_date) = YEAR(CURDATE())");
    $monthly_revenue = $stmt->fetch()['monthly_revenue'] ?? 0;
    
    // العيادات الجديدة هذا الشهر
    $stmt = $pdo->query("SELECT COUNT(*) as new_clinics FROM clinics WHERE MONTH(created_at) = MONTH(CURDATE()) AND YEAR(created_at) = YEAR(CURDATE())");
    $new_clinics = $stmt->fetch()['new_clinics'];
    
    // العيادات التي ستنتهي صلاحيتها خلال 7 أيام
    $stmt = $pdo->query("SELECT COUNT(*) as expiring_soon FROM clinics WHERE is_active = 1 AND subscription_end BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 7 DAY)");
    $expiring_soon = $stmt->fetch()['expiring_soon'];
    
    // أحدث العيادات المسجلة
    $stmt = $pdo->query("SELECT clinic_name, owner_name, email, created_at, subscription_plan FROM clinics ORDER BY created_at DESC LIMIT 5");
    $recent_clinics = $stmt->fetchAll();
    
    // إحصائيات الاشتراكات
    $stmt = $pdo->query("SELECT subscription_plan, COUNT(*) as count FROM clinics WHERE is_active = 1 GROUP BY subscription_plan");
    $subscription_stats = $stmt->fetchAll();
    
} catch (Exception $e) {
    $error_message = 'حدث خطأ في جلب البيانات';
}
?>
<!DOCTYPE html>
<html lang="<?php echo $lang; ?>" dir="<?php echo $lang == 'ar' ? 'rtl' : 'ltr'; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم الإدارة - <?php echo $text['site_title']; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../assets/css/style.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body class="bg-light">
    <div class="container-fluid">
        <div class="row">
            <!-- الشريط الجانبي -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h4 class="text-primary fw-bold">
                            <i class="fas fa-user-shield me-2"></i>
                            لوحة الإدارة
                        </h4>
                        <small class="text-muted">مرحباً، <?php echo $_SESSION['full_name']; ?></small>
                    </div>
                    
                    <ul class="sidebar-nav">
                        <li>
                            <a href="dashboard.php" class="active">
                                <i class="fas fa-tachometer-alt"></i>
                                الرئيسية
                            </a>
                        </li>
                        <li>
                            <a href="clinics.php">
                                <i class="fas fa-hospital"></i>
                                إدارة العيادات
                            </a>
                        </li>
                        <li>
                            <a href="subscriptions.php">
                                <i class="fas fa-credit-card"></i>
                                الاشتراكات
                            </a>
                        </li>
                        <li>
                            <a href="payments.php">
                                <i class="fas fa-money-bill-wave"></i>
                                المدفوعات
                            </a>
                        </li>
                        <li>
                            <a href="reports.php">
                                <i class="fas fa-chart-bar"></i>
                                التقارير
                            </a>
                        </li>
                        <li>
                            <a href="settings.php">
                                <i class="fas fa-cog"></i>
                                الإعدادات
                            </a>
                        </li>
                        <li>
                            <a href="users.php">
                                <i class="fas fa-users-cog"></i>
                                المستخدمين
                            </a>
                        </li>
                        <li>
                            <a href="../public/login.php?logout=1" class="text-danger">
                                <i class="fas fa-sign-out-alt"></i>
                                تسجيل الخروج
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
            
            <!-- المحتوى الرئيسي -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-tachometer-alt me-2"></i>
                        لوحة التحكم الرئيسية
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-download me-1"></i>
                                تصدير التقرير
                            </button>
                        </div>
                        <button type="button" class="btn btn-sm btn-primary">
                            <i class="fas fa-plus me-1"></i>
                            إضافة عيادة
                        </button>
                    </div>
                </div>
                
                <!-- بطاقات الإحصائيات -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="stats-card bg-primary text-white">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <div class="stats-number"><?php echo number_format($total_clinics); ?></div>
                                    <div class="stats-label">إجمالي العيادات</div>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-hospital fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="stats-card bg-success text-white">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <div class="stats-number"><?php echo number_format($active_clinics); ?></div>
                                    <div class="stats-label">العيادات النشطة</div>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-check-circle fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="stats-card bg-warning text-white">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <div class="stats-number"><?php echo number_format($expiring_soon); ?></div>
                                    <div class="stats-label">تنتهي قريباً</div>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-exclamation-triangle fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="stats-card bg-info text-white">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <div class="stats-number"><?php echo format_currency($monthly_revenue); ?></div>
                                    <div class="stats-label">الإيرادات الشهرية</div>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-money-bill-wave fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- الصف الثاني من الإحصائيات -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="stats-card">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <div class="stats-number text-primary"><?php echo number_format($total_patients); ?></div>
                                    <div class="stats-label">إجمالي المرضى</div>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-users fa-2x text-primary opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="stats-card">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <div class="stats-number text-success"><?php echo number_format($today_appointments); ?></div>
                                    <div class="stats-label">مواعيد اليوم</div>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-calendar-day fa-2x text-success opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="stats-card">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <div class="stats-number text-info"><?php echo number_format($new_clinics); ?></div>
                                    <div class="stats-label">عيادات جديدة</div>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-plus-circle fa-2x text-info opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="stats-card">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <div class="stats-number text-danger"><?php echo number_format($expired_clinics); ?></div>
                                    <div class="stats-label">منتهية الصلاحية</div>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-times-circle fa-2x text-danger opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <!-- الرسوم البيانية -->
                    <div class="col-lg-8 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-chart-line me-2"></i>
                                    إحصائيات الاشتراكات
                                </h5>
                            </div>
                            <div class="card-body">
                                <canvas id="subscriptionChart" height="100"></canvas>
                            </div>
                        </div>
                    </div>
                    
                    <!-- أحدث العيادات -->
                    <div class="col-lg-4 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-hospital me-2"></i>
                                    أحدث العيادات
                                </h5>
                            </div>
                            <div class="card-body">
                                <?php if (!empty($recent_clinics)): ?>
                                    <?php foreach ($recent_clinics as $clinic): ?>
                                        <div class="d-flex align-items-center mb-3">
                                            <div class="flex-shrink-0">
                                                <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                                    <i class="fas fa-hospital text-white"></i>
                                                </div>
                                            </div>
                                            <div class="flex-grow-1 ms-3">
                                                <h6 class="mb-0"><?php echo htmlspecialchars($clinic['clinic_name']); ?></h6>
                                                <small class="text-muted"><?php echo htmlspecialchars($clinic['owner_name']); ?></small>
                                                <br>
                                                <span class="badge badge-<?php echo $clinic['subscription_plan'] == 'basic' ? 'info' : ($clinic['subscription_plan'] == 'pro' ? 'warning' : 'success'); ?>">
                                                    <?php echo $clinic['subscription_plan']; ?>
                                                </span>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <p class="text-muted text-center">لا توجد عيادات مسجلة حديثاً</p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- جدول العيادات التي تحتاج متابعة -->
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    العيادات التي تحتاج متابعة
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>اسم العيادة</th>
                                                <th>المالك</th>
                                                <th>البريد الإلكتروني</th>
                                                <th>تاريخ انتهاء الاشتراك</th>
                                                <th>الحالة</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php
                                            $stmt = $pdo->query("SELECT * FROM clinics WHERE is_active = 1 AND (subscription_end < CURDATE() OR subscription_end BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 7 DAY)) ORDER BY subscription_end ASC LIMIT 10");
                                            $urgent_clinics = $stmt->fetchAll();
                                            
                                            if (!empty($urgent_clinics)):
                                                foreach ($urgent_clinics as $clinic):
                                                    $days_left = (strtotime($clinic['subscription_end']) - strtotime(date('Y-m-d'))) / (60 * 60 * 24);
                                                    $status_class = $days_left < 0 ? 'danger' : ($days_left <= 3 ? 'warning' : 'info');
                                                    $status_text = $days_left < 0 ? 'منتهية' : ($days_left <= 3 ? 'تنتهي قريباً' : 'نشطة');
                                            ?>
                                                <tr>
                                                    <td><?php echo htmlspecialchars($clinic['clinic_name']); ?></td>
                                                    <td><?php echo htmlspecialchars($clinic['owner_name']); ?></td>
                                                    <td><?php echo htmlspecialchars($clinic['email']); ?></td>
                                                    <td><?php echo format_date_arabic($clinic['subscription_end']); ?></td>
                                                    <td>
                                                        <span class="badge bg-<?php echo $status_class; ?>">
                                                            <?php echo $status_text; ?>
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <div class="btn-group btn-group-sm">
                                                            <a href="clinic_details.php?id=<?php echo $clinic['id']; ?>" class="btn btn-outline-primary">
                                                                <i class="fas fa-eye"></i>
                                                            </a>
                                                            <a href="extend_subscription.php?id=<?php echo $clinic['id']; ?>" class="btn btn-outline-success">
                                                                <i class="fas fa-plus"></i>
                                                            </a>
                                                            <a href="mailto:<?php echo $clinic['email']; ?>" class="btn btn-outline-info">
                                                                <i class="fas fa-envelope"></i>
                                                            </a>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php 
                                                endforeach;
                                            else:
                                            ?>
                                                <tr>
                                                    <td colspan="6" class="text-center text-muted">
                                                        <i class="fas fa-check-circle me-2"></i>
                                                        جميع العيادات في حالة جيدة
                                                    </td>
                                                </tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="../assets/js/main.js"></script>
    
    <script>
        // رسم بياني للاشتراكات
        const ctx = document.getElementById('subscriptionChart').getContext('2d');
        const subscriptionChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['أساسي', 'متقدم', 'مؤسسات'],
                datasets: [{
                    data: [
                        <?php
                        $basic = $pro = $enterprise = 0;
                        foreach ($subscription_stats as $stat) {
                            if ($stat['subscription_plan'] == 'basic') $basic = $stat['count'];
                            if ($stat['subscription_plan'] == 'pro') $pro = $stat['count'];
                            if ($stat['subscription_plan'] == 'enterprise') $enterprise = $stat['count'];
                        }
                        echo "$basic, $pro, $enterprise";
                        ?>
                    ],
                    backgroundColor: [
                        '#3b82f6',
                        '#f59e0b',
                        '#10b981'
                    ],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
        
        // تحديث الإحصائيات كل 5 دقائق
        setInterval(function() {
            location.reload();
        }, 300000);
    </script>
</body>
</html>
