# دليل نشر نظام حكيم على الاستضافة

## معلومات الاستضافة المقدمة

- **الرابط**: https://xyz.collectandwin.xyz/
- **اسم قاعدة البيانات**: collectandwin2_vitalsmm
- **اسم المستخدم**: collectandwin2_vitalsmm
- **كلمة المرور**: collectandwin2_vitalsmm

## خطوات النشر

### 1. تحضير الملفات

قم بضغط جميع ملفات المشروع في ملف ZIP باستثناء:
- ملف README.md
- ملف deployment-guide.md
- أي ملفات تطوير أخرى

### 2. رفع الملفات

1. قم بتسجيل الدخول إلى لوحة تحكم الاستضافة
2. انتقل إلى مدير الملفات (File Manager)
3. انتقل إلى مجلد public_html أو المجلد الرئيسي للموقع
4. ارفع ملف ZIP وقم بفك الضغط
5. تأكد من أن ملف index.php في المجلد الرئيسي

### 3. إعداد قاعدة البيانات

#### الطريقة الأولى: استيراد ملف SQL
1. انتقل إلى phpMyAdmin في لوحة التحكم
2. اختر قاعدة البيانات: collectandwin2_vitalsmm
3. انقر على "Import" أو "استيراد"
4. ارفع ملف database.sql
5. انقر على "Go" أو "تنفيذ"

#### الطريقة الثانية: إنشاء تلقائي
- عند زيارة الموقع لأول مرة، سيتم إنشاء الجداول تلقائياً

### 4. تحديث إعدادات قاعدة البيانات

تأكد من أن ملف `config/database.php` يحتوي على البيانات الصحيحة:

```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'collectandwin2_vitalsmm');
define('DB_USER', 'collectandwin2_vitalsmm');
define('DB_PASS', 'collectandwin2_vitalsmm');
```

### 5. إعداد الصلاحيات

تأكد من أن المجلدات التالية لها صلاحيات الكتابة (755 أو 777):
- `uploads/`
- `assets/`

### 6. اختبار النظام

1. انتقل إلى رابط الموقع: https://xyz.collectandwin.xyz/
2. يجب أن تظهر الصفحة الرئيسية بشكل صحيح
3. جرب تسجيل الدخول بحساب الإدارة:
   - اسم المستخدم: admin
   - كلمة المرور: admin123

### 7. إعدادات الأمان

#### تغيير كلمة مرور الإدارة
1. سجل دخول كمدير
2. انتقل إلى الإعدادات
3. غير كلمة المرور الافتراضية

#### تحديث معلومات الاتصال
قم بتحديث معلومات الاتصال في:
- ملفات اللغة (lang/ar.php و lang/en.php)
- الصفحة الرئيسية
- صفحة الاتصال

### 8. إعدادات البريد الإلكتروني

لتفعيل إرسال البريد الإلكتروني، قم بتحديث دالة `send_email_notification` في `includes/functions.php`:

```php
function send_email_notification($to, $subject, $message) {
    // استخدم SMTP أو خدمة البريد المتاحة في الاستضافة
    $headers = "From: <EMAIL>\r\n";
    $headers .= "Reply-To: <EMAIL>\r\n";
    $headers .= "Content-Type: text/html; charset=UTF-8\r\n";
    
    return mail($to, $subject, $message, $headers);
}
```

### 9. إعدادات SSL

إذا كان لديك شهادة SSL:
1. فعل إعادة التوجيه من HTTP إلى HTTPS في ملف .htaccess
2. قم بإلغاء التعليق عن الأسطر التالية:

```apache
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
```

### 10. النسخ الاحتياطية

#### إعداد نسخ احتياطية تلقائية
1. أنشئ مجلد `backups/` خارج public_html
2. أضف مهمة cron job لعمل نسخة احتياطية يومية:

```bash
0 2 * * * mysqldump -u collectandwin2_vitalsmm -pcollectandwin2_vitalsmm collectandwin2_vitalsmm > /path/to/backups/backup_$(date +\%Y\%m\%d).sql
```

### 11. مراقبة الأداء

#### تفعيل سجلات الأخطاء
أضف إلى ملف .htaccess:
```apache
php_flag log_errors On
php_value error_log /path/to/error.log
```

#### مراقبة استخدام قاعدة البيانات
راقب حجم قاعدة البيانات وأداء الاستعلامات

### 12. تحسين الأداء

#### تفعيل التخزين المؤقت
- تأكد من تفعيل mod_expires و mod_deflate
- استخدم CDN للملفات الثابتة إذا أمكن

#### ضغط الصور
- ضغط الصور في مجلد assets/images/
- استخدم تنسيقات حديثة مثل WebP

### 13. اختبار الوظائف

اختبر الوظائف التالية:
- [ ] تسجيل عيادة جديدة
- [ ] تسجيل الدخول والخروج
- [ ] إضافة مريض جديد
- [ ] حجز موعد
- [ ] إنشاء وصفة طبية
- [ ] إنشاء فاتورة
- [ ] عرض التقارير
- [ ] تغيير اللغة

### 14. إعدادات SEO

#### إضافة meta tags
أضف في head كل صفحة:
```html
<meta name="description" content="نظام حكيم لإدارة العيادات الطبية">
<meta name="keywords" content="إدارة عيادات, نظام طبي, مواعيد, وصفات">
<meta name="author" content="فريق حكيم">
```

#### إنشاء ملف robots.txt
```
User-agent: *
Allow: /
Disallow: /admin/
Disallow: /clinic/
Disallow: /config/
Disallow: /includes/
Disallow: /uploads/

Sitemap: https://xyz.collectandwin.xyz/sitemap.xml
```

### 15. الدعم الفني

#### معلومات الاتصال
- البريد الإلكتروني: <EMAIL>
- الهاتف: [رقم الهاتف]

#### سجل التحديثات
احتفظ بسجل للتحديثات والتغييرات:
- تاريخ النشر
- الإصدار
- التغييرات المضافة
- المشاكل المحلولة

### 16. مشاكل شائعة وحلولها

#### مشكلة: لا تظهر الصور أو CSS
**الحل**: تحقق من صلاحيات المجلدات والمسارات

#### مشكلة: خطأ في الاتصال بقاعدة البيانات
**الحل**: تحقق من بيانات الاتصال في config/database.php

#### مشكلة: لا يعمل إرسال البريد الإلكتروني
**الحل**: تحقق من إعدادات SMTP في الاستضافة

#### مشكلة: بطء في التحميل
**الحل**: 
- فعل الضغط والتخزين المؤقت
- حسن استعلامات قاعدة البيانات
- استخدم CDN

### 17. تحديثات مستقبلية

#### خطة التحديث
1. إنشاء نسخة احتياطية
2. اختبار التحديث على بيئة تطوير
3. تطبيق التحديث على الموقع الحي
4. اختبار جميع الوظائف

#### ميزات مخططة
- تطبيق موبايل
- تكامل مع أنظمة الدفع
- تقارير متقدمة
- API للتكامل الخارجي

---

## ملاحظات مهمة

1. **الأمان**: غير كلمات المرور الافتراضية فوراً
2. **النسخ الاحتياطية**: أنشئ نسخ احتياطية منتظمة
3. **التحديثات**: راقب التحديثات الأمنية
4. **المراقبة**: راقب أداء الموقع والأخطاء
5. **الدعم**: احتفظ بمعلومات الاتصال محدثة

## جهات الاتصال للدعم

- **المطور**: [معلومات الاتصال]
- **الاستضافة**: [معلومات دعم الاستضافة]
- **الطوارئ**: [رقم الطوارئ]

---

**تم إنشاء هذا الدليل لضمان نشر ناجح وآمن لنظام حكيم**
