<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// تحديد اللغة
$lang = isset($_GET['lang']) ? $_GET['lang'] : (isset($_SESSION['lang']) ? $_SESSION['lang'] : 'ar');
$_SESSION['lang'] = $lang;
require_once "../lang/{$lang}.php";

// إعادة توجيه المستخدمين المسجلين
if (isset($_SESSION['user_id'])) {
    if ($_SESSION['user_type'] == 'admin') {
        header('Location: ../admin/dashboard.php');
    } else {
        header('Location: ../clinic/dashboard.php');
    }
    exit();
}

$error_message = '';
$success_message = '';

// معالجة التسجيل
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $clinic_name = sanitize_input($_POST['clinic_name']);
    $clinic_name_en = sanitize_input($_POST['clinic_name_en']);
    $owner_name = sanitize_input($_POST['owner_name']);
    $email = sanitize_input($_POST['email']);
    $phone = sanitize_input($_POST['phone']);
    $address = sanitize_input($_POST['address']);
    $city = sanitize_input($_POST['city']);
    $specialization = sanitize_input($_POST['specialization']);
    $username = sanitize_input($_POST['username']);
    $password = $_POST['password'];
    $confirm_password = $_POST['confirm_password'];
    $agree_terms = isset($_POST['agree_terms']);
    
    // التحقق من البيانات
    if (empty($clinic_name) || empty($owner_name) || empty($email) || empty($phone) || empty($username) || empty($password)) {
        $error_message = 'يرجى ملء جميع الحقول المطلوبة';
    } elseif (!validate_email($email)) {
        $error_message = 'البريد الإلكتروني غير صحيح';
    } elseif (!validate_password($password)) {
        $error_message = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
    } elseif ($password !== $confirm_password) {
        $error_message = 'كلمة المرور وتأكيد كلمة المرور غير متطابقتين';
    } elseif (!$agree_terms) {
        $error_message = 'يجب الموافقة على الشروط والأحكام';
    } else {
        try {
            // التحقق من عدم وجود البريد الإلكتروني مسبقاً
            $stmt = $pdo->prepare("SELECT id FROM clinics WHERE email = ?");
            $stmt->execute([$email]);
            if ($stmt->fetch()) {
                $error_message = 'البريد الإلكتروني مستخدم مسبقاً';
            } else {
                // بدء المعاملة
                $pdo->beginTransaction();
                
                // إنشاء العيادة
                $subscription_start = date('Y-m-d');
                $subscription_end = date('Y-m-d', strtotime('+7 days')); // تجربة مجانية 7 أيام
                
                $stmt = $pdo->prepare("INSERT INTO clinics (clinic_name, clinic_name_en, owner_name, email, phone, address, city, specialization, subscription_plan, subscription_start, subscription_end, trial_used) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'basic', ?, ?, 1)");
                $stmt->execute([$clinic_name, $clinic_name_en, $owner_name, $email, $phone, $address, $city, $specialization, $subscription_start, $subscription_end]);
                
                $clinic_id = $pdo->lastInsertId();
                
                // إنشاء مستخدم المالك
                $hashed_password = hash_password($password);
                $stmt = $pdo->prepare("INSERT INTO clinic_users (clinic_id, username, email, password, full_name, phone, role, specialization) VALUES (?, ?, ?, ?, ?, ?, 'owner', ?)");
                $stmt->execute([$clinic_id, $username, $email, $hashed_password, $owner_name, $phone, $specialization]);
                
                // إضافة بعض الأدوية الأساسية
                $basic_medications = [
                    ['باراسيتامول', 'Paracetamol', '500mg', 'أقراص', 'قرص واحد كل 6 ساعات'],
                    ['إيبوبروفين', 'Ibuprofen', '400mg', 'أقراص', 'قرص واحد كل 8 ساعات'],
                    ['أموكسيسيلين', 'Amoxicillin', '500mg', 'كبسولات', 'كبسولة كل 8 ساعات'],
                    ['أوميبرازول', 'Omeprazole', '20mg', 'كبسولات', 'كبسولة واحدة قبل الإفطار'],
                    ['لوراتادين', 'Loratadine', '10mg', 'أقراص', 'قرص واحد يومياً']
                ];
                
                foreach ($basic_medications as $med) {
                    $stmt = $pdo->prepare("INSERT INTO medications (clinic_id, medication_name, medication_name_en, dosage, form, instructions) VALUES (?, ?, ?, ?, ?, ?)");
                    $stmt->execute([$clinic_id, $med[0], $med[1], $med[2], $med[3], $med[4]]);
                }
                
                // تأكيد المعاملة
                $pdo->commit();
                
                // إرسال بريد ترحيب
                $subject = 'مرحباً بك في منصة حكيم';
                $message = "
                    <h2>مرحباً {$owner_name}</h2>
                    <p>تم إنشاء حساب عيادة {$clinic_name} بنجاح في منصة حكيم.</p>
                    <p><strong>بيانات الدخول:</strong></p>
                    <ul>
                        <li>اسم المستخدم: {$username}</li>
                        <li>البريد الإلكتروني: {$email}</li>
                    </ul>
                    <p>يمكنك الآن تسجيل الدخول والاستفادة من التجربة المجانية لمدة 7 أيام.</p>
                    <p>مع تحيات فريق حكيم</p>
                ";
                
                send_email_notification($email, $subject, $message);
                
                $success_message = 'تم إنشاء الحساب بنجاح! يمكنك الآن تسجيل الدخول والاستفادة من التجربة المجانية لمدة 7 أيام.';
            }
        } catch (Exception $e) {
            $pdo->rollBack();
            $error_message = 'حدث خطأ في النظام. يرجى المحاولة مرة أخرى.';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="<?php echo $lang; ?>" dir="<?php echo $lang == 'ar' ? 'rtl' : 'ltr'; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $text['register_title']; ?> - <?php echo $text['site_title']; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card shadow-lg border-0">
                    <div class="card-header bg-gradient-primary text-white text-center py-4">
                        <h2 class="fw-bold mb-0">
                            <i class="fas fa-hospital me-2"></i>
                            <?php echo $text['register_title']; ?>
                        </h2>
                        <p class="mb-0 mt-2">ابدأ تجربتك المجانية لمدة 7 أيام</p>
                    </div>
                    
                    <div class="card-body p-5">
                        <?php if ($error_message): ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <?php echo $error_message; ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($success_message): ?>
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-2"></i>
                                <?php echo $success_message; ?>
                                <div class="mt-3">
                                    <a href="login.php" class="btn btn-success">
                                        <i class="fas fa-sign-in-alt me-2"></i>
                                        تسجيل الدخول الآن
                                    </a>
                                </div>
                            </div>
                        <?php else: ?>
                            <form method="POST" class="needs-validation" novalidate>
                                <div class="row">
                                    <!-- بيانات العيادة -->
                                    <div class="col-12">
                                        <h5 class="text-primary mb-3">
                                            <i class="fas fa-hospital me-2"></i>
                                            بيانات العيادة
                                        </h5>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="clinic_name" class="form-label">
                                            <?php echo $text['clinic_name']; ?> <span class="text-danger">*</span>
                                        </label>
                                        <input type="text" class="form-control" id="clinic_name" name="clinic_name" required
                                               value="<?php echo isset($_POST['clinic_name']) ? htmlspecialchars($_POST['clinic_name']) : ''; ?>">
                                        <div class="invalid-feedback">يرجى إدخال اسم العيادة</div>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="clinic_name_en" class="form-label">
                                            اسم العيادة بالإنجليزية
                                        </label>
                                        <input type="text" class="form-control" id="clinic_name_en" name="clinic_name_en"
                                               value="<?php echo isset($_POST['clinic_name_en']) ? htmlspecialchars($_POST['clinic_name_en']) : ''; ?>">
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="specialization" class="form-label">
                                            <?php echo $text['specialization']; ?>
                                        </label>
                                        <select class="form-select" id="specialization" name="specialization">
                                            <option value="">اختر التخصص</option>
                                            <option value="طب عام">طب عام</option>
                                            <option value="طب أطفال">طب أطفال</option>
                                            <option value="طب نساء وولادة">طب نساء وولادة</option>
                                            <option value="طب عيون">طب عيون</option>
                                            <option value="طب أسنان">طب أسنان</option>
                                            <option value="طب جلدية">طب جلدية</option>
                                            <option value="طب نفسي">طب نفسي</option>
                                            <option value="طب قلب">طب قلب</option>
                                            <option value="طب عظام">طب عظام</option>
                                            <option value="أخرى">أخرى</option>
                                        </select>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="city" class="form-label">
                                            <?php echo $text['city']; ?>
                                        </label>
                                        <input type="text" class="form-control" id="city" name="city"
                                               value="<?php echo isset($_POST['city']) ? htmlspecialchars($_POST['city']) : ''; ?>">
                                    </div>
                                    
                                    <div class="col-12 mb-3">
                                        <label for="address" class="form-label">
                                            <?php echo $text['address']; ?>
                                        </label>
                                        <textarea class="form-control" id="address" name="address" rows="2"><?php echo isset($_POST['address']) ? htmlspecialchars($_POST['address']) : ''; ?></textarea>
                                    </div>
                                    
                                    <!-- بيانات المالك -->
                                    <div class="col-12 mt-4">
                                        <h5 class="text-primary mb-3">
                                            <i class="fas fa-user-md me-2"></i>
                                            بيانات المالك
                                        </h5>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="owner_name" class="form-label">
                                            <?php echo $text['owner_name']; ?> <span class="text-danger">*</span>
                                        </label>
                                        <input type="text" class="form-control" id="owner_name" name="owner_name" required
                                               value="<?php echo isset($_POST['owner_name']) ? htmlspecialchars($_POST['owner_name']) : ''; ?>">
                                        <div class="invalid-feedback">يرجى إدخال اسم المالك</div>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="phone" class="form-label">
                                            <?php echo $text['phone']; ?> <span class="text-danger">*</span>
                                        </label>
                                        <input type="tel" class="form-control" id="phone" name="phone" required
                                               value="<?php echo isset($_POST['phone']) ? htmlspecialchars($_POST['phone']) : ''; ?>">
                                        <div class="invalid-feedback">يرجى إدخال رقم الهاتف</div>
                                    </div>
                                    
                                    <div class="col-12 mb-3">
                                        <label for="email" class="form-label">
                                            <?php echo $text['email']; ?> <span class="text-danger">*</span>
                                        </label>
                                        <input type="email" class="form-control" id="email" name="email" required
                                               value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>">
                                        <div class="invalid-feedback">يرجى إدخال بريد إلكتروني صحيح</div>
                                    </div>
                                    
                                    <!-- بيانات الدخول -->
                                    <div class="col-12 mt-4">
                                        <h5 class="text-primary mb-3">
                                            <i class="fas fa-key me-2"></i>
                                            بيانات الدخول
                                        </h5>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="username" class="form-label">
                                            اسم المستخدم <span class="text-danger">*</span>
                                        </label>
                                        <input type="text" class="form-control" id="username" name="username" required
                                               value="<?php echo isset($_POST['username']) ? htmlspecialchars($_POST['username']) : ''; ?>">
                                        <div class="invalid-feedback">يرجى إدخال اسم المستخدم</div>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="password" class="form-label">
                                            <?php echo $text['password']; ?> <span class="text-danger">*</span>
                                        </label>
                                        <input type="password" class="form-control" id="password" name="password" required minlength="6">
                                        <div class="invalid-feedback">كلمة المرور يجب أن تكون 6 أحرف على الأقل</div>
                                    </div>
                                    
                                    <div class="col-12 mb-3">
                                        <label for="confirm_password" class="form-label">
                                            <?php echo $text['confirm_password']; ?> <span class="text-danger">*</span>
                                        </label>
                                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                                        <div class="invalid-feedback">يرجى تأكيد كلمة المرور</div>
                                    </div>
                                    
                                    <div class="col-12 mb-4">
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="agree_terms" name="agree_terms" required>
                                            <label class="form-check-label" for="agree_terms">
                                                <?php echo $text['agree_terms']; ?> <span class="text-danger">*</span>
                                            </label>
                                            <div class="invalid-feedback">يجب الموافقة على الشروط والأحكام</div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-12">
                                        <div class="d-grid">
                                            <button type="submit" class="btn btn-primary btn-lg">
                                                <i class="fas fa-rocket me-2"></i>
                                                <?php echo $text['register_button']; ?>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        <?php endif; ?>
                        
                        <hr class="my-4">
                        
                        <div class="text-center">
                            <p class="mb-2"><?php echo $text['have_account']; ?></p>
                            <a href="login.php" class="btn btn-outline-primary">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                <?php echo $text['login']; ?>
                            </a>
                        </div>
                    </div>
                </div>
                
                <!-- تغيير اللغة -->
                <div class="text-center mt-3">
                    <div class="btn-group" role="group">
                        <a href="?lang=ar" class="btn btn-sm <?php echo $lang == 'ar' ? 'btn-primary' : 'btn-outline-primary'; ?>">
                            العربية
                        </a>
                        <a href="?lang=en" class="btn btn-sm <?php echo $lang == 'en' ? 'btn-primary' : 'btn-outline-primary'; ?>">
                            English
                        </a>
                    </div>
                </div>
                
                <!-- رابط العودة للصفحة الرئيسية -->
                <div class="text-center mt-3">
                    <a href="../index.php" class="text-muted text-decoration-none">
                        <i class="fas fa-arrow-left me-2"></i>
                        العودة للصفحة الرئيسية
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="../assets/js/main.js"></script>
    
    <script>
        // التحقق من تطابق كلمة المرور
        document.getElementById('confirm_password').addEventListener('input', function() {
            var password = document.getElementById('password').value;
            var confirmPassword = this.value;
            
            if (password !== confirmPassword) {
                this.setCustomValidity('كلمة المرور غير متطابقة');
            } else {
                this.setCustomValidity('');
            }
        });
        
        // إنشاء اسم مستخدم تلقائي من اسم العيادة
        document.getElementById('clinic_name').addEventListener('input', function() {
            var clinicName = this.value;
            var username = clinicName.replace(/\s+/g, '').toLowerCase();
            document.getElementById('username').value = username;
        });
    </script>
</body>
</html>
